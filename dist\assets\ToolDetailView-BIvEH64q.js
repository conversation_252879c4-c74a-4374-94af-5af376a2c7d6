import{b as a,s as l,_ as e,u as s,d as t,c as i}from"./index-DfMIV_22.js";import{d as n,r as o,c as r,o as c,m as u,q as v,E as d,F as p,A as g,z as h,u as y,a4 as f,C as m,af as k,R as _,U as w,H as b,T as C,t as x,B as I,aR as $,I as M,G as q,aC as T,aD as D}from"./vendor-CnBwbdGX.js";const z={class:"tool-rating"},A={class:"rating-header"},H={class:"rating-summary"},L={class:"average-rating"},R={class:"rating-score"},U={class:"stars"},V={class:"rating-count"},j={class:"rating-distribution"},E={class:"rating-label"},F={class:"bar-container"},W={class:"rating-count"},B={key:0,class:"user-rating-section"},G={class:"rating-input"},N={class:"stars-input"},S={class:"rating-text"},J={class:"rating-actions"},K={class:"anonymous-checkbox"},O=["disabled"],P={key:1,class:"login-prompt"},Q={class:"reviews-section"},X={key:0,class:"no-reviews"},Y={key:1,class:"reviews-list"},Z={class:"review-header"},aa={class:"reviewer-info"},la={class:"reviewer-name"},ea={class:"review-rating"},sa={class:"review-date"},ta={key:0,class:"review-content"},ia=e(n({__name:"ToolRating",props:{toolId:{}},setup(e){const s=e,t=a(),i=o([]),n=o([]),$=o(null),M=o(0),q=o(0),T=o(""),D=o(!1),ia=o(!1),na=r(()=>{if(0===i.value.length)return 0;return i.value.reduce((a,l)=>a+l.rating,0)/i.value.length}),oa=r(()=>i.value.length),ra=r(()=>{const a={5:0,4:0,3:0,2:0,1:0};return i.value.forEach(l=>{a[l.rating]++}),a});c(async()=>{await ca(),await ua()});const ca=async()=>{try{const{data:a,error:e}=await l.from("tool_ratings").select("\n        *,\n        user_profiles (username)\n      ").eq("tool_id",s.toolId).order("created_at",{ascending:!1});if(e)throw e;i.value=a||[],n.value=(null==a?void 0:a.filter(a=>a.review))||[]}catch(a){console.error("加载评分失败:",a)}},ua=async()=>{if(t.user)try{const{data:a,error:e}=await l.from("tool_ratings").select("*").eq("tool_id",s.toolId).eq("user_id",t.user.id).single();a&&($.value=a,M.value=a.rating,T.value=a.review||"",D.value=a.is_anonymous)}catch(a){}},va=a=>0===oa.value?0:a/oa.value*100,da=async()=>{if(M.value&&t.user)try{ia.value=!0;const a={tool_id:s.toolId,user_id:t.user.id,rating:M.value,review:T.value.trim()||null,is_anonymous:D.value};if($.value){const{error:e}=await l.from("tool_ratings").update(a).eq("id",$.value.id);if(e)throw e}else{const{error:e}=await l.from("tool_ratings").insert(a);if(e)throw e}await ca(),await ua()}catch(a){console.error("提交评分失败:",a),alert("提交失败，请重试")}finally{ia.value=!1}};return(a,l)=>{return I(),u("div",z,[v("div",A,[l[4]||(l[4]=v("h3",null,"用户评价",-1)),v("div",H,[v("div",L,[v("span",R,d(na.value.toFixed(1)),1),v("div",U,[(I(),u(p,null,g(5,a=>h(y(f),{key:a,class:m([{filled:a<=Math.round(na.value)},"star"])},null,8,["class"])),64))]),v("span",V,"("+d(oa.value)+" 评价)",1)])])]),v("div",j,[(I(!0),u(p,null,g(ra.value,(a,l)=>(I(),u("div",{key:l,class:"rating-bar"},[v("span",E,d(l)+"星",1),v("div",F,[v("div",{class:"bar",style:k({width:va(a)+"%"})},null,4)]),v("span",W,d(a),1)]))),128))]),y(t).isAuthenticated?(I(),u("div",B,[v("h4",null,d($.value?"修改评价":"添加评价"),1),v("div",G,[v("div",N,[(I(),u(p,null,g(5,a=>h(y(f),{key:a,class:m([{filled:a<=M.value,hover:a<=q.value},"star clickable"]),onClick:l=>{return e=a,void(M.value=e);var e},onMouseenter:l=>q.value=a,onMouseleave:l[0]||(l[0]=a=>q.value=0)},null,8,["class","onClick","onMouseenter"])),64))]),v("span",S,d((e=M.value,["","很差","一般","不错","很好","极佳"][e]||"")),1)]),_(v("textarea",{"onUpdate:modelValue":l[1]||(l[1]=a=>T.value=a),placeholder:"分享您的使用体验（可选）",class:"review-input",rows:"3"},null,512),[[w,T.value]]),v("div",J,[v("label",K,[_(v("input",{type:"checkbox","onUpdate:modelValue":l[2]||(l[2]=a=>D.value=a)},null,512),[[C,D.value]]),l[5]||(l[5]=b(" 匿名评价 "))]),v("button",{onClick:da,disabled:!M.value||ia.value,class:"submit-button"},d(ia.value?"提交中...":$.value?"更新评价":"提交评价"),9,O)])])):(I(),u("div",P,[l[6]||(l[6]=v("p",null,"登录后可以评价工具",-1)),v("button",{onClick:l[3]||(l[3]=l=>a.$router.push("/auth/login")),class:"login-button"},"立即登录")])),v("div",Q,[v("h4",null,"用户评价 ("+d(n.value.length)+")",1),0===n.value.length?(I(),u("div",X," 暂无评价，成为第一个评价者吧！ ")):(I(),u("div",Y,[(I(!0),u(p,null,g(n.value,a=>{var l,e;return I(),u("div",{key:a.id,class:"review-item"},[v("div",Z,[v("div",aa,[v("span",la,d(a.is_anonymous?"匿名用户":(null==(l=a.user_profiles)?void 0:l.username)||"用户"),1),v("div",ea,[(I(),u(p,null,g(5,l=>h(y(f),{key:l,class:m([{filled:l<=a.rating},"star small"])},null,8,["class"])),64))])]),v("span",sa,d((e=a.created_at,new Date(e).toLocaleDateString("zh-CN"))),1)]),a.review?(I(),u("p",ta,d(a.review),1)):x("",!0)])}),128))]))])]);var e}}}),[["__scopeId","data-v-a1280faf"]]),na={class:"tool-detail-view"},oa={class:"container"},ra={key:0,class:"tool-detail-card"},ca={class:"tool-header"},ua={class:"tool-icon"},va={class:"tool-info"},da={class:"tool-name"},pa={class:"tool-description"},ga={class:"tool-meta"},ha={class:"category"},ya={class:"clicks"},fa={class:"tool-actions"},ma={key:0,class:"features-section"},ka={class:"features-grid"},_a={key:1,class:"tutorial-section"},wa=["innerHTML"],ba={key:2,class:"video-section"},Ca={class:"video-container"},xa=["src"],Ia={key:3,class:"pros-cons-section"},$a={class:"pros-cons-grid"},Ma={class:"pros"},qa={class:"cons"},Ta={key:4,class:"pricing-section"},Da={class:"pricing-content"},za={class:"related-tools-section"},Aa={class:"related-tools-grid"},Ha=["onClick"],La={class:"tool-icon"},Ra={class:"tool-name"},Ua={key:1,class:"loading-state"},Va={key:2,class:"error-state"},ja=e(n({__name:"ToolDetailView",setup(l){const e=t(),n=i(),k=s(),_=a(),w=o(null),C=o(!0),z=o([]),A=r(()=>!1);c(async()=>{await H()});const H=async()=>{try{C.value=!0;const a=e.params.id;await k.initialize(),w.value=k.tools.find(l=>l.id===a),w.value&&(z.value=k.tools.filter(a=>a.category_id===w.value.category_id&&a.id!==w.value.id).slice(0,4))}catch(a){console.error("加载工具详情失败:",a)}finally{C.value=!1}},L=()=>{n.back()},R=async()=>{var a;if(null==(a=w.value)?void 0:a.url)try{let a=w.value.url.trim();a.startsWith("http://")||a.startsWith("https://")||(a="https://"+a),await k.incrementClickCount(w.value.id),window.open(a,"_blank","noopener,noreferrer")}catch(l){console.error("打开链接失败:",l),alert("无法打开该链接")}else alert("该工具暂无可用链接")},U=()=>{_.isAuthenticated||n.push("/auth/login")};return(a,l)=>{var e,s;return I(),u("div",na,[v("div",oa,[v("button",{onClick:L,class:"back-button"},[h(y($),{class:"icon"}),l[0]||(l[0]=b(" 返回工具列表 "))]),w.value?(I(),u("div",ra,[v("div",ca,[v("div",ua,d(w.value.icon||"🔧"),1),v("div",va,[v("h1",da,d(w.value.name),1),v("p",pa,d(w.value.description),1),v("div",ga,[v("span",ha,d(null==(e=w.value.categories)?void 0:e.name),1),v("span",ya,d(w.value.click_count)+" 次使用",1)])]),v("div",fa,[v("button",{onClick:R,class:"primary-button"},[h(y(M),{class:"icon"}),l[1]||(l[1]=b(" 访问工具 "))]),v("button",{onClick:U,class:m(["favorite-button",{favorited:A.value}])},[h(y(f),{class:"icon"}),b(" "+d(A.value?"已收藏":"收藏"),1)],2)])]),w.value.features?(I(),u("div",ma,[l[2]||(l[2]=v("h2",null,"主要功能",-1)),v("div",ka,[(I(!0),u(p,null,g(w.value.features,a=>(I(),u("div",{key:a,class:"feature-item"},[h(y(q),{class:"icon"}),b(" "+d(a),1)]))),128))])])):x("",!0),w.value.tutorial_content?(I(),u("div",_a,[l[3]||(l[3]=v("h2",null,"使用教程",-1)),v("div",{class:"tutorial-content",innerHTML:(s=w.value.tutorial_content,s.replace(/### (.*)/g,"<h3>$1</h3>").replace(/## (.*)/g,"<h2>$1</h2>").replace(/# (.*)/g,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/\n/g,"<br>"))},null,8,wa)])):x("",!0),w.value.tutorial_video_url?(I(),u("div",ba,[l[4]||(l[4]=v("h2",null,"视频教程",-1)),v("div",Ca,[v("iframe",{src:w.value.tutorial_video_url,frameborder:"0",allowfullscreen:""},null,8,xa)])])):x("",!0),w.value.pros_cons?(I(),u("div",Ia,[l[7]||(l[7]=v("h2",null,"优缺点分析",-1)),v("div",$a,[v("div",Ma,[v("h3",null,[h(y(T),{class:"icon"}),l[5]||(l[5]=b(" 优点"))]),v("ul",null,[(I(!0),u(p,null,g(w.value.pros_cons.pros,a=>(I(),u("li",{key:a},d(a),1))),128))])]),v("div",qa,[v("h3",null,[h(y(D),{class:"icon"}),l[6]||(l[6]=b(" 缺点"))]),v("ul",null,[(I(!0),u(p,null,g(w.value.pros_cons.cons,a=>(I(),u("li",{key:a},d(a),1))),128))])])])])):x("",!0),w.value.pricing_info?(I(),u("div",Ta,[l[8]||(l[8]=v("h2",null,"价格信息",-1)),v("div",Da,d(w.value.pricing_info),1)])):x("",!0),h(ia,{"tool-id":w.value.id},null,8,["tool-id"]),v("div",za,[l[9]||(l[9]=v("h2",null,"相关工具推荐",-1)),v("div",Aa,[(I(!0),u(p,null,g(z.value,a=>(I(),u("div",{key:a.id,class:"related-tool-card",onClick:l=>{return e=a.id,void n.push(`/tools/${e}`);var e}},[v("div",La,d(a.icon||"🔧"),1),v("div",Ra,d(a.name),1)],8,Ha))),128))])])])):C.value?(I(),u("div",Ua,l[10]||(l[10]=[v("div",{class:"spinner"},null,-1),v("p",null,"正在加载工具详情...",-1)]))):(I(),u("div",Va,[l[11]||(l[11]=v("p",null,"工具不存在或加载失败",-1)),v("button",{onClick:L,class:"secondary-button"},"返回")]))])])}}}),[["__scopeId","data-v-8bc771c3"]]);export{ja as default};
