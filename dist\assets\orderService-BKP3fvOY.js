import{s as t}from"./index-DfMIV_22.js";import"./vendor-CnBwbdGX.js";class e{static async createOrder(e,r){try{const{data:a,error:d}=await t.from("products").select("id, name, price, is_digital").eq("id",e.productId).eq("status","active").single();if(d||!a)throw new Error("产品不存在或已下架");const o=a.price*e.quantity,{data:i,error:s}=await t.from("orders").insert({user_id:r,total_amount:o,currency:"CNY",status:"pending",billing_address:e.billingAddress}).select().single();if(s)throw s;const{data:n,error:c}=await t.from("order_items").insert({order_id:i.id,product_id:e.productId,quantity:e.quantity,unit_price:a.price,total_price:o}).select().single();if(c)throw c;return{id:i.id,userId:i.user_id,items:[{id:n.id,orderId:i.id,productId:e.productId,quantity:e.quantity,unitPrice:a.price,totalPrice:o,createdAt:n.created_at,product:{id:a.id,name:a.name,shortDescription:"",images:[]}}],totalAmount:o,currency:i.currency,status:i.status,billingAddress:e.billingAddress,createdAt:i.created_at,updatedAt:i.updated_at}}catch(a){throw console.error("创建订单失败:",a),new Error("创建订单失败")}}static async processPayment(e){try{const{error:r}=await t.from("orders").update({status:"paid",payment_method:e.paymentMethod,payment_id:e.paymentId,completed_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}).eq("id",e.orderId).eq("status","pending");if(r)throw r}catch(r){throw console.error("处理支付失败:",r),new Error("处理支付失败")}}static async verifyDownloadPermission(e,r){try{const{data:a,error:d}=await t.from("order_items").select("\n          id,\n          orders!inner(\n            id,\n            user_id,\n            status\n          )\n        ").eq("product_id",e).eq("orders.user_id",r).eq("orders.status","paid");if(d)throw d;return a&&a.length>0}catch(a){return console.error("验证下载权限失败:",a),!1}}static async getUserOrders(e){try{const{data:r,error:a}=await t.from("orders").select("\n          *,\n          order_items(\n            *,\n            products(id, name, images, short_description)\n          )\n        ").eq("user_id",e).order("created_at",{ascending:!1});if(a)throw a;return r.map(t=>({id:t.id,userId:t.user_id,items:t.order_items.map(t=>({id:t.id,orderId:t.order_id,productId:t.product_id,quantity:t.quantity,unitPrice:t.unit_price,totalPrice:t.total_price,createdAt:t.created_at,product:t.products?{id:t.products.id,name:t.products.name,shortDescription:t.products.short_description||"",images:t.products.images||[]}:void 0})),totalAmount:t.total_amount,currency:t.currency,status:t.status,paymentMethod:t.payment_method,paymentId:t.payment_id,billingAddress:t.billing_address,createdAt:t.created_at,updatedAt:t.updated_at,completedAt:t.completed_at}))}catch(r){throw console.error("获取用户订单失败:",r),new Error("获取用户订单失败")}}static async cancelOrder(e,r){try{const{error:a}=await t.from("orders").update({status:"cancelled",updated_at:(new Date).toISOString()}).eq("id",e).eq("user_id",r).eq("status","pending");if(a)throw a}catch(a){throw console.error("取消订单失败:",a),new Error("取消订单失败")}}static async getOrderById(e,r){try{const{data:a,error:d}=await t.from("orders").select("\n          *,\n          order_items(\n            *,\n            products(id, name, images, short_description, download_url)\n          )\n        ").eq("id",e).eq("user_id",r).single();if(d)throw d;return a?{id:a.id,userId:a.user_id,items:a.order_items.map(t=>({id:t.id,orderId:t.order_id,productId:t.product_id,quantity:t.quantity,unitPrice:t.unit_price,totalPrice:t.total_price,createdAt:t.created_at,product:t.products?{id:t.products.id,name:t.products.name,shortDescription:t.products.short_description||"",images:t.products.images||[],downloadUrl:t.products.download_url}:void 0})),totalAmount:a.total_amount,currency:a.currency,status:a.status,paymentMethod:a.payment_method,paymentId:a.payment_id,billingAddress:a.billing_address,createdAt:a.created_at,updatedAt:a.updated_at,completedAt:a.completed_at}:null}catch(a){return console.error("获取订单详情失败:",a),null}}static async getAllOrders(e){try{const r=(null==e?void 0:e.page)||1,a=(null==e?void 0:e.limit)||10,d=(r-1)*a;let o=t.from("orders").select("\n          *,\n          user_profiles!inner(id, email, full_name, avatar_url),\n          order_items(\n            *,\n            products(id, name, images, short_description)\n          )\n        ",{count:"exact"});if((null==e?void 0:e.status)&&(o=o.eq("status",e.status)),(null==e?void 0:e.paymentMethod)&&(o=o.eq("payment_method",e.paymentMethod)),null==e?void 0:e.search){const t=e.search.toLowerCase();o=o.or(`id.ilike.%${t}%,user_profiles.email.ilike.%${t}%,user_profiles.full_name.ilike.%${t}%`)}if((null==e?void 0:e.startDate)&&(o=o.gte("created_at",e.startDate)),null==e?void 0:e.endDate){const t=new Date(e.endDate);t.setHours(23,59,59,999),o=o.lte("created_at",t.toISOString())}o=o.order("created_at",{ascending:!1}).range(d,d+a-1);const{data:i,error:s,count:n}=await o;if(s)throw s;return{orders:(i||[]).map(t=>({id:t.id,userId:t.user_id,user:t.user_profiles?{id:t.user_profiles.id,email:t.user_profiles.email,full_name:t.user_profiles.full_name,avatar_url:t.user_profiles.avatar_url}:void 0,items:t.order_items.map(t=>({id:t.id,orderId:t.order_id,productId:t.product_id,quantity:t.quantity,unitPrice:t.unit_price,totalPrice:t.total_price,createdAt:t.created_at,product:t.products?{id:t.products.id,name:t.products.name,shortDescription:t.products.short_description||"",images:t.products.images||[]}:void 0})),totalAmount:t.total_amount,currency:t.currency,status:t.status,paymentMethod:t.payment_method,paymentId:t.payment_id,billingAddress:t.billing_address,createdAt:t.created_at,updatedAt:t.updated_at,completedAt:t.completed_at})),total:n||0,page:r,limit:a}}catch(r){throw console.error("获取所有订单失败:",r),new Error("获取所有订单失败")}}static async updateOrderStatus(e,r,a){try{const d={status:r,updated_at:(new Date).toISOString()};"paid"===r&&(d.completed_at=(new Date).toISOString());const{error:o}=await t.from("orders").update(d).eq("id",e);if(o)throw o;console.log(`管理员 ${a} 将订单 ${e} 状态更新为 ${r}`)}catch(d){throw console.error("更新订单状态失败:",d),new Error("更新订单状态失败")}}static async getOrderStats(){try{const{data:e,error:r}=await t.from("orders").select("status, total_amount, created_at");if(r)throw r;const a=new Date;a.setHours(0,0,0,0);const d=a.toISOString(),{data:o,error:i}=await t.from("orders").select("status, total_amount").gte("created_at",d);if(i)throw i;const s=(null==e?void 0:e.length)||0,n=(null==e?void 0:e.filter(t=>"pending"===t.status).length)||0,c=(null==e?void 0:e.filter(t=>"paid"===t.status).length)||0,u=(null==e?void 0:e.filter(t=>"cancelled"===t.status).length)||0,l=(null==e?void 0:e.filter(t=>"paid"===t.status).reduce((t,e)=>t+e.total_amount,0))||0,p=(null==o?void 0:o.length)||0;return{totalOrders:s,pendingOrders:n,paidOrders:c,cancelledOrders:u,totalRevenue:l,todayOrders:p,todayRevenue:(null==o?void 0:o.filter(t=>"paid"===t.status).reduce((t,e)=>t+e.total_amount,0))||0}}catch(e){throw console.error("获取订单统计失败:",e),new Error("获取订单统计失败")}}static async exportOrders(e){try{let r=t.from("orders").select("\n          *,\n          user_profiles!inner(email, full_name),\n          order_items(\n            *,\n            products(name)\n          )\n        ");if((null==e?void 0:e.status)&&(r=r.eq("status",e.status)),(null==e?void 0:e.startDate)&&(r=r.gte("created_at",e.startDate)),null==e?void 0:e.endDate){const t=new Date(e.endDate);t.setHours(23,59,59,999),r=r.lte("created_at",t.toISOString())}r=r.order("created_at",{ascending:!1});const{data:a,error:d}=await r;if(d)throw d;let o=["订单ID","用户邮箱","用户姓名","商品名称","数量","单价","总金额","货币","状态","支付方式","支付ID","创建时间","完成时间"].join(",")+"\n";return null==a||a.forEach(t=>{t.order_items.forEach(e=>{var r,a,d;const i=[t.id,(null==(r=t.user_profiles)?void 0:r.email)||"",(null==(a=t.user_profiles)?void 0:a.full_name)||"",(null==(d=e.products)?void 0:d.name)||"",e.quantity,e.unit_price,t.total_amount,t.currency,t.status,t.payment_method||"",t.payment_id||"",new Date(t.created_at).toLocaleString("zh-CN"),t.completed_at?new Date(t.completed_at).toLocaleString("zh-CN"):""];o+=i.map(t=>`"${t}"`).join(",")+"\n"})}),o}catch(r){throw console.error("导出订单数据失败:",r),new Error("导出订单数据失败")}}}export{e as OrderService};
