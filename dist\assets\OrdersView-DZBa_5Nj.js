const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./orderService-BKP3fvOY.js","./index-DfMIV_22.js","./vendor-CnBwbdGX.js","./index-CK0XRuJs.css"])))=>i.map(i=>d[i]);
import{f as e,c as t,_ as a}from"./index-DfMIV_22.js";import{d as r,r as s,c as l,o as i,m as o,q as n,F as d,A as c,C as u,H as p,E as m,t as v,z as y,u as g,aU as h,$ as k,_ as w,B as _}from"./vendor-CnBwbdGX.js";const f={class:"orders-view"},S={class:"orders-filters"},b=["onClick"],A={class:"filter-count"},I={class:"orders-content"},O={key:0,class:"loading-state"},D={key:1,class:"orders-list"},C={class:"order-header"},P={class:"order-info"},E={class:"order-number"},j={class:"order-date"},T={class:"order-status"},q={class:"order-items"},x={class:"item-image"},L=["src","alt"],U={class:"item-info"},V={class:"item-name"},R={class:"item-description"},$={class:"item-meta"},z={class:"item-quantity"},N={class:"item-price"},B={class:"item-total"},Y={class:"order-footer"},F={class:"order-total"},H={class:"total-amount"},M={class:"order-actions"},G=["onClick"],J=["onClick"],K=["onClick"],Q=["onClick"],W={key:2,class:"empty-state"},X=a(r({__name:"OrdersView",setup(a){const r=t(),X=s(!0),Z=s("all"),ee=s([]),te=[{key:"all",label:"全部订单"},{key:"pending",label:"待支付"},{key:"paid",label:"已支付"},{key:"cancelled",label:"已取消"},{key:"refunded",label:"已退款"}],ae=l(()=>"all"===Z.value?ee.value:ee.value.filter(e=>e.status===Z.value));return i(()=>{(async()=>{try{X.value=!0;const{OrderService:a}=await e(async()=>{const{OrderService:e}=await import("./orderService-BKP3fvOY.js");return{OrderService:e}},__vite__mapDeps([0,1,2,3]),import.meta.url),{useAuthStore:r}=await e(async()=>{const{useAuthStore:e}=await import("./index-DfMIV_22.js").then(e=>e.g);return{useAuthStore:e}},__vite__mapDeps([1,2,3]),import.meta.url),s=r();if(!s.user)return void console.error("用户未登录");try{const e=await a.getUserOrders(s.user.id);return void(ee.value=e)}catch(t){console.warn("加载真实订单数据失败，使用模拟数据:",t)}await new Promise(e=>setTimeout(e,1e3)),ee.value=[{id:"order-1",userId:"user-1",items:[{id:"item-1",orderId:"order-1",productId:"product-1",quantity:1,unitPrice:299,totalPrice:299,createdAt:(new Date).toISOString(),product:{id:"product-1",name:"高效办公套件",shortDescription:"提升办公效率的完整解决方案",images:["/placeholder.jpg"]}}],totalAmount:299,currency:"CNY",status:"paid",paymentMethod:"alipay",createdAt:new Date(Date.now()-864e5).toISOString(),updatedAt:(new Date).toISOString(),completedAt:(new Date).toISOString()},{id:"order-2",userId:"user-1",items:[{id:"item-2",orderId:"order-2",productId:"product-2",quantity:1,unitPrice:199,totalPrice:199,createdAt:(new Date).toISOString(),product:{id:"product-2",name:"设计师工具包",shortDescription:"专业设计师必备工具集合",images:["/placeholder.jpg"]}}],totalAmount:199,currency:"CNY",status:"pending",createdAt:new Date(Date.now()-36e5).toISOString(),updatedAt:(new Date).toISOString()}]}catch(a){console.error("加载订单失败:",a)}finally{X.value=!1}})()}),(t,a)=>{const s=w("router-link");return _(),o("div",f,[a[5]||(a[5]=n("div",{class:"orders-header"},[n("h1",null,"我的订单"),n("p",null,"查看和管理您的订单历史")],-1)),n("div",S,[(_(),o(d,null,c(te,e=>{return n("button",{key:e.key,class:u(["filter-btn",{active:Z.value===e.key}]),onClick:t=>Z.value=e.key},[p(m(e.label)+" ",1),n("span",A,m((t=e.key,"all"===t?ee.value.length:ee.value.filter(e=>e.status===t).length)),1)],10,b);var t}),64))]),n("div",I,[X.value?(_(),o("div",O,a[0]||(a[0]=[n("div",{class:"loading-spinner"},null,-1),n("p",null,"正在加载订单...",-1)]))):ae.value.length>0?(_(),o("div",D,[(_(!0),o(d,null,c(ae.value,t=>{return _(),o("div",{key:t.id,class:"order-item"},[n("div",C,[n("div",P,[n("h3",E," 订单号: "+m(t.id.slice(-8).toUpperCase()),1),n("p",j," 下单时间: "+m((l=t.createdAt,new Date(l).toLocaleString("zh-CN"))),1)]),n("div",T,[n("span",{class:u(["status-badge",t.status])},m((s=t.status,{pending:"待支付",paid:"已支付",cancelled:"已取消",refunded:"已退款"}[s]||s)),3)])]),n("div",q,[(_(!0),o(d,null,c(t.items,e=>{var t,a,r,s,l;return _(),o("div",{key:e.id,class:"order-item-detail"},[n("div",x,[n("img",{src:(null==(a=null==(t=e.product)?void 0:t.images)?void 0:a[0])||"/placeholder.jpg",alt:null==(r=e.product)?void 0:r.name},null,8,L)]),n("div",U,[n("h4",V,m(null==(s=e.product)?void 0:s.name),1),n("p",R,m(null==(l=e.product)?void 0:l.shortDescription),1),n("div",$,[n("span",z,"数量: "+m(e.quantity),1),n("span",N,"单价: ¥"+m(e.unitPrice),1)])]),n("div",B,"¥"+m(e.totalPrice),1)])}),128))]),n("div",Y,[n("div",F,[a[1]||(a[1]=n("span",{class:"total-label"},"订单总额:",-1)),n("span",H,"¥"+m(t.totalAmount),1)]),n("div",M,["pending"===t.status?(_(),o("button",{key:0,class:"action-btn primary",onClick:e=>(e=>{r.push(`/payment?order=${e.id}`)})(t)}," 立即支付 ",8,G)):v("",!0),"pending"===t.status?(_(),o("button",{key:1,class:"action-btn secondary",onClick:e=>(async e=>{if(confirm("确定要取消这个订单吗？"))try{const t=ee.value.findIndex(t=>t.id===e.id);-1!==t&&(ee.value[t].status="cancelled")}catch(t){console.error("取消订单失败:",t)}})(t)}," 取消订单 ",8,J)):v("",!0),"paid"===t.status?(_(),o("button",{key:2,class:"action-btn secondary",onClick:a=>(async t=>{var a;try{if(console.log("下载订单产品:",t.id),"paid"!==t.status)return void alert("订单未支付，无法下载");const{OrderService:r}=await e(async()=>{const{OrderService:e}=await import("./orderService-BKP3fvOY.js");return{OrderService:e}},__vite__mapDeps([0,1,2,3]),import.meta.url),{useAuthStore:s}=await e(async()=>{const{useAuthStore:e}=await import("./index-DfMIV_22.js").then(e=>e.g);return{useAuthStore:e}},__vite__mapDeps([1,2,3]),import.meta.url),l=s();if(!l.user)return void alert("请先登录");const i=await r.getOrderById(t.id,l.user.id);if(!i||"paid"!==i.status)return void alert("订单状态异常，无法下载");let o=0;for(const e of i.items)if(null==(a=e.product)?void 0:a.downloadUrl){const t=document.createElement("a");t.href=e.product.downloadUrl,t.download=`${e.product.name}.zip`,t.target="_blank",document.body.appendChild(t),t.click(),document.body.removeChild(t),o++,await new Promise(e=>setTimeout(e,500))}0===o?alert("该订单中没有可下载的产品"):console.log(`成功启动 ${o} 个产品的下载`)}catch(r){console.error("下载失败:",r),alert("下载失败，请稍后重试")}})(t)},[y(g(h),{class:"icon"}),a[2]||(a[2]=p(" 下载产品 "))],8,K)):v("",!0),n("button",{class:"action-btn secondary",onClick:e=>(e=>{console.log("查看订单详情:",e.id)})(t)}," 查看详情 ",8,Q)])])]);var s,l}),128))])):(_(),o("div",W,[a[4]||(a[4]=n("div",{class:"empty-icon"},"📦",-1)),n("h3",null,m({all:"暂无订单",pending:"暂无待支付订单",paid:"暂无已支付订单",cancelled:"暂无已取消订单",refunded:"暂无已退款订单"}[Z.value]||"暂无订单"),1),n("p",null,m({all:"您还没有任何订单，去购买一些产品吧！",pending:"您没有待支付的订单",paid:"您没有已支付的订单",cancelled:"您没有已取消的订单",refunded:"您没有已退款的订单"}[Z.value]||"暂无相关订单"),1),y(s,{to:"/products",class:"empty-action"},{default:k(()=>a[3]||(a[3]=[p("去购买产品")])),_:1,__:[3]})]))])])}}}),[["__scopeId","data-v-90d66720"]]);export{X as default};
