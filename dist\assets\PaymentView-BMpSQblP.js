const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./orderService-BKP3fvOY.js","./index-DfMIV_22.js","./vendor-CnBwbdGX.js","./index-CK0XRuJs.css"])))=>i.map(i=>d[i]);
import{d as e,f as a,c as l,_ as s}from"./index-DfMIV_22.js";import{d as t,r as i,c as r,o as n,m as o,q as d,t as u,F as c,A as m,E as p,C as v,R as y,V as h,z as f,u as g,G as w,U as q,ae as _,B as b}from"./vendor-CnBwbdGX.js";const V={class:"payment-view"},S={class:"payment-container"},k={class:"payment-content"},A={class:"order-summary"},E={class:"order-items"},U={class:"item-image"},N=["src","alt"],P={class:"item-info"},x={class:"item-name"},I={class:"item-description"},O={class:"item-meta"},j={class:"item-quantity"},C={class:"item-price"},D={class:"order-total"},M={class:"total-row"},R={class:"total-row"},T={class:"discount"},L={class:"total-row final"},$={class:"final-amount"},z={class:"payment-methods"},B={class:"methods-list"},F=["value"],G={class:"method-content"},J={class:"method-icon"},Y=["src","alt"],H={class:"method-info"},K={class:"method-name"},Q={class:"method-description"},W={class:"method-check"},X={class:"billing-address"},Z={class:"address-form"},ee={class:"form-row"},ae={class:"form-group"},le={class:"form-group"},se={class:"form-row"},te={class:"form-group"},ie={class:"form-group"},re={class:"form-group"},ne={class:"payment-actions"},oe=["disabled"],de={key:0,class:"loading-spinner"},ue={key:0,class:"error-message"},ce=s(t({__name:"PaymentView",setup(s){const t=l(),ce=e(),me=i(!1),pe=i(null),ve=i(""),ye=i([{id:"1",name:"高效办公套件",description:"提升办公效率的完整解决方案",quantity:1,price:299,image:"/placeholder.jpg"}]),he=i({fullName:"",email:"",country:"",city:"",address:""}),fe=[{id:"alipay",name:"支付宝",description:"使用支付宝安全快捷支付",icon:"/payment-alipay.png"},{id:"wechat",name:"微信支付",description:"使用微信支付便捷支付",icon:"/payment-wechat.png"},{id:"stripe",name:"信用卡",description:"支持 Visa、MasterCard 等",icon:"/payment-stripe.png"}],ge=r(()=>ye.value.reduce((e,a)=>e+a.price*a.quantity,0)),we=r(()=>0),qe=r(()=>ge.value-we.value),_e=r(()=>ve.value&&he.value.fullName&&he.value.email&&he.value.country&&he.value.city&&he.value.address),be=async()=>{try{me.value=!0,pe.value=null;const{OrderService:e}=await a(async()=>{const{OrderService:e}=await import("./orderService-BKP3fvOY.js");return{OrderService:e}},__vite__mapDeps([0,1,2,3]),import.meta.url),{useAuthStore:l}=await a(async()=>{const{useAuthStore:e}=await import("./index-DfMIV_22.js").then(e=>e.g);return{useAuthStore:e}},__vite__mapDeps([1,2,3]),import.meta.url),s=l();if(!s.user)throw new Error("请先登录");let i=ce.query.order;if(!i){const a=ce.query.product;if(!a)throw new Error("缺少产品信息");const l={productId:a,quantity:1,billingAddress:he.value};i=(await e.createOrder(l,s.user.id)).id}await new Promise(e=>setTimeout(e,2e3)),await e.processPayment({orderId:i,paymentMethod:ve.value,paymentId:`PAY_${Date.now()}`,amount:qe.value}),t.push({path:"/payment/success",query:{order:i,amount:qe.value.toString()}})}catch(e){pe.value=e instanceof Error?e.message:"支付失败，请重试"}finally{me.value=!1}},Ve=()=>{t.go(-1)};return n(()=>{(()=>{const e=ce.query.product,a=ce.query.order;e?console.log("从产品创建订单:",e):a&&console.log("加载现有订单:",a)})(),fe.length>0&&(ve.value=fe[0].id)}),(e,a)=>(b(),o("div",V,[d("div",S,[a[18]||(a[18]=d("div",{class:"payment-header"},[d("h1",null,"支付订单"),d("p",null,"请选择支付方式完成购买")],-1)),d("div",k,[d("div",A,[a[9]||(a[9]=d("h3",null,"订单信息",-1)),d("div",E,[(b(!0),o(c,null,m(ye.value,e=>(b(),o("div",{key:e.id,class:"order-item"},[d("div",U,[d("img",{src:e.image,alt:e.name},null,8,N)]),d("div",P,[d("h4",x,p(e.name),1),d("p",I,p(e.description),1),d("div",O,[d("span",j,"数量: "+p(e.quantity),1),d("span",C,"¥"+p(e.price),1)])])]))),128))]),d("div",D,[d("div",M,[a[6]||(a[6]=d("span",null,"商品总额",-1)),d("span",null,"¥"+p(ge.value),1)]),d("div",R,[a[7]||(a[7]=d("span",null,"优惠折扣",-1)),d("span",T,"-¥"+p(we.value),1)]),d("div",L,[a[8]||(a[8]=d("span",null,"应付金额",-1)),d("span",$,"¥"+p(qe.value),1)])])]),d("div",z,[a[10]||(a[10]=d("h3",null,"选择支付方式",-1)),d("div",B,[(b(),o(c,null,m(fe,e=>d("label",{key:e.id,class:v(["method-option",{selected:ve.value===e.id}])},[y(d("input",{"onUpdate:modelValue":a[0]||(a[0]=e=>ve.value=e),type:"radio",value:e.id,name:"paymentMethod"},null,8,F),[[h,ve.value]]),d("div",G,[d("div",J,[d("img",{src:e.icon,alt:e.name},null,8,Y)]),d("div",H,[d("div",K,p(e.name),1),d("div",Q,p(e.description),1)]),d("div",W,[f(g(w),{class:"check-icon"})])])],2)),64))])]),d("div",X,[a[17]||(a[17]=d("h3",null,"账单地址",-1)),d("form",Z,[d("div",ee,[d("div",ae,[a[11]||(a[11]=d("label",{for:"fullName"},"姓名 *",-1)),y(d("input",{id:"fullName","onUpdate:modelValue":a[1]||(a[1]=e=>he.value.fullName=e),type:"text",required:"",placeholder:"请输入姓名"},null,512),[[q,he.value.fullName]])]),d("div",le,[a[12]||(a[12]=d("label",{for:"email"},"邮箱 *",-1)),y(d("input",{id:"email","onUpdate:modelValue":a[2]||(a[2]=e=>he.value.email=e),type:"email",required:"",placeholder:"请输入邮箱地址"},null,512),[[q,he.value.email]])])]),d("div",se,[d("div",te,[a[14]||(a[14]=d("label",{for:"country"},"国家/地区 *",-1)),y(d("select",{id:"country","onUpdate:modelValue":a[3]||(a[3]=e=>he.value.country=e),required:""},a[13]||(a[13]=[d("option",{value:""},"请选择国家/地区",-1),d("option",{value:"CN"},"中国",-1),d("option",{value:"US"},"美国",-1),d("option",{value:"JP"},"日本",-1)]),512),[[_,he.value.country]])]),d("div",ie,[a[15]||(a[15]=d("label",{for:"city"},"城市 *",-1)),y(d("input",{id:"city","onUpdate:modelValue":a[4]||(a[4]=e=>he.value.city=e),type:"text",required:"",placeholder:"请输入城市"},null,512),[[q,he.value.city]])])]),d("div",re,[a[16]||(a[16]=d("label",{for:"address"},"详细地址 *",-1)),y(d("input",{id:"address","onUpdate:modelValue":a[5]||(a[5]=e=>he.value.address=e),type:"text",required:"",placeholder:"请输入详细地址"},null,512),[[q,he.value.address]])])])]),d("div",ne,[d("button",{class:"cancel-btn",onClick:Ve},"取消订单"),d("button",{class:"pay-btn",disabled:!_e.value||me.value,onClick:be},[me.value?(b(),o("div",de)):u("",!0),d("span",null,p(me.value?"处理中...":`立即支付 ¥${qe.value}`),1)],8,oe)]),pe.value?(b(),o("div",ue,p(pe.value),1)):u("",!0)])])]))}}),[["__scopeId","data-v-df653159"]]);export{ce as default};
