import{d as a,r as s,b as e,o as l,m as n,q as t,t as i,z as c,H as o,C as r,u as d,b0 as u,ag as v,E as m,b2 as b,aZ as p,aQ as g,aY as f,aU as y,b5 as h,ba as w,bb as S,aI as k,B}from"./vendor-CnBwbdGX.js";import{_ as M}from"./index-DfMIV_22.js";const C={class:"local-management-view"},x={class:"page-header"},L={class:"header-actions"},E=["disabled"],O={class:"management-grid"},z={class:"management-card"},T={class:"card-header"},j={class:"card-content"},_={class:"card-stats"},F={class:"stat-item"},I={class:"stat-value"},U={class:"stat-item"},A={class:"stat-value"},R={class:"card-actions"},$=["disabled"],D=["disabled"],P={class:"management-card"},q={class:"card-header"},H={class:"card-content"},J={class:"card-stats"},W={class:"stat-item"},Z={class:"stat-value"},G={class:"stat-item"},K={class:"stat-value"},N={class:"card-actions"},Q=["disabled"],V={class:"management-card"},Y={class:"card-header"},X={class:"card-content"},aa={class:"card-stats"},sa={class:"stat-item"},ea={class:"stat-value"},la={class:"stat-item"},na={class:"stat-value text-red-600"},ta={class:"card-actions"},ia=["disabled"],ca={class:"management-card"},oa={class:"card-header"},ra={class:"card-content"},da={class:"card-stats"},ua={class:"stat-item"},va={class:"stat-value"},ma={class:"stat-item"},ba={class:"stat-value"},pa={class:"card-actions"},ga=["disabled"],fa={class:"system-info-section"},ya={class:"info-grid"},ha={class:"info-item"},wa={class:"info-value"},Sa={class:"info-item"},ka={class:"info-value"},Ba={class:"info-item"},Ma={class:"info-value"},Ca={class:"info-item"},xa={class:"info-value"},La={class:"info-item"},Ea={class:"info-value"},Oa={class:"info-item"},za={key:0,class:"loading-overlay"},Ta={class:"loading-spinner"},ja=M(a({__name:"LocalManagementView",setup(a){const M=s(!1),ja=e({size:"0 MB",count:0}),_a=e({used:"0 MB",available:"0 MB"}),Fa=e({count:0,errors:0}),Ia=e({memory:"0 MB",loadTime:"0 ms"}),Ua=e({browser:"",os:"",screen:"",timezone:"",language:"",online:!0}),Aa=async()=>{M.value=!0;try{await Promise.all([Ra(),$a(),Da(),Pa(),qa()])}catch(a){console.error("刷新数据失败:",a)}finally{M.value=!1}},Ra=async()=>{ja.size="2.5 MB",ja.count=156},$a=async()=>{try{if("storage"in navigator&&"estimate"in navigator.storage){const a=await navigator.storage.estimate(),s=a.usage||0,e=a.quota||0;_a.used=Ha(s),_a.available=Ha(e-s)}else _a.used="未知",_a.available="未知"}catch(a){console.error("获取存储统计失败:",a),_a.used="获取失败",_a.available="获取失败"}},Da=async()=>{Fa.count=1234,Fa.errors=5},Pa=async()=>{try{if("memory"in performance){const a=performance.memory;Ia.memory=Ha(a.usedJSHeapSize)}else Ia.memory="未知";const a=performance.getEntriesByType("navigation")[0];if(a){const s=a.loadEventEnd-a.navigationStart;Ia.loadTime=`${Math.round(s)} ms`}}catch(a){console.error("获取性能统计失败:",a)}},qa=()=>{Ua.browser=Ja(),Ua.os=Wa(),Ua.screen=`${screen.width}x${screen.height}`,Ua.timezone=Intl.DateTimeFormat().resolvedOptions().timeZone,Ua.language=navigator.language,Ua.online=navigator.onLine},Ha=a=>{if(0===a)return"0 B";const s=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,s)).toFixed(2))+" "+["B","KB","MB","GB"][s]},Ja=()=>{const a=navigator.userAgent;return a.includes("Chrome")?"Chrome":a.includes("Firefox")?"Firefox":a.includes("Safari")?"Safari":a.includes("Edge")?"Edge":"未知"},Wa=()=>{const a=navigator.userAgent;return a.includes("Windows")?"Windows":a.includes("Mac")?"macOS":a.includes("Linux")?"Linux":a.includes("Android")?"Android":a.includes("iOS")?"iOS":"未知"},Za=async()=>{if(confirm("确定要清理所有缓存吗？")){M.value=!0;try{if("caches"in window){const a=await caches.keys();await Promise.all(a.map(a=>caches.delete(a)))}await Ra(),alert("缓存清理完成")}catch(a){console.error("清理缓存失败:",a),alert("清理缓存失败")}finally{M.value=!1}}},Ga=async()=>{M.value=!0;try{window.location.reload()}catch(a){console.error("刷新缓存失败:",a)}finally{M.value=!1}},Ka=async()=>{if(confirm("确定要清理本地存储吗？这将清除所有保存的数据！")){M.value=!0;try{localStorage.clear(),sessionStorage.clear(),await $a(),alert("本地存储清理完成")}catch(a){console.error("清理存储失败:",a),alert("清理存储失败")}finally{M.value=!1}}},Na=()=>{const a={localStorage:{...localStorage},sessionStorage:{...sessionStorage}};console.log("本地存储数据:",a),alert("存储数据已输出到控制台")},Qa=()=>{console.log("查看应用程序日志"),alert("日志数据已输出到控制台")},Va=()=>{const a=JSON.stringify({timestamp:(new Date).toISOString(),logs:"模拟日志数据"},null,2),s=new Blob([a],{type:"application/json"}),e=URL.createObjectURL(s),l=document.createElement("a");l.href=e,l.download=`logs_${(new Date).toISOString().split("T")[0]}.json`,l.click(),URL.revokeObjectURL(e)},Ya=async()=>{M.value=!0;try{await new Promise(a=>setTimeout(a,2e3)),await Pa(),alert("性能测试完成")}catch(a){console.error("性能测试失败:",a)}finally{M.value=!1}},Xa=()=>{console.log("性能报告:",Ia),alert("性能报告已输出到控制台")};return l(()=>{Aa(),window.addEventListener("online",()=>{Ua.online=!0}),window.addEventListener("offline",()=>{Ua.online=!1})}),(a,s)=>(B(),n("div",C,[t("div",x,[s[1]||(s[1]=t("h1",{class:"page-title"},"本地管理",-1)),t("div",L,[t("button",{onClick:Aa,class:"btn btn-primary",disabled:M.value},[c(d(u),{class:r({"animate-spin":M.value})},null,8,["class"]),s[0]||(s[0]=o(" 刷新数据 "))],8,E)])]),t("div",O,[t("div",z,[t("div",T,[c(d(v),{class:"card-icon"}),s[2]||(s[2]=t("h3",null,"缓存管理",-1))]),t("div",j,[s[7]||(s[7]=t("p",{class:"card-description"},"管理应用程序缓存和临时数据",-1)),t("div",_,[t("div",F,[s[3]||(s[3]=t("span",{class:"stat-label"},"缓存大小:",-1)),t("span",I,m(ja.size),1)]),t("div",U,[s[4]||(s[4]=t("span",{class:"stat-label"},"缓存项数:",-1)),t("span",A,m(ja.count),1)])]),t("div",R,[t("button",{onClick:Za,class:"btn btn-outline",disabled:M.value},[c(d(b)),s[5]||(s[5]=o(" 清理缓存 "))],8,$),t("button",{onClick:Ga,class:"btn btn-outline",disabled:M.value},[c(d(u)),s[6]||(s[6]=o(" 刷新缓存 "))],8,D)])])]),t("div",P,[t("div",q,[c(d(p),{class:"card-icon"}),s[8]||(s[8]=t("h3",null,"本地存储",-1))]),t("div",H,[s[13]||(s[13]=t("p",{class:"card-description"},"管理浏览器本地存储数据",-1)),t("div",J,[t("div",W,[s[9]||(s[9]=t("span",{class:"stat-label"},"存储使用:",-1)),t("span",Z,m(_a.used),1)]),t("div",G,[s[10]||(s[10]=t("span",{class:"stat-label"},"可用空间:",-1)),t("span",K,m(_a.available),1)])]),t("div",N,[t("button",{onClick:Na,class:"btn btn-outline"},[c(d(g)),s[11]||(s[11]=o(" 查看数据 "))]),t("button",{onClick:Ka,class:"btn btn-outline",disabled:M.value},[c(d(b)),s[12]||(s[12]=o(" 清理存储 "))],8,Q)])])]),t("div",V,[t("div",Y,[c(d(f),{class:"card-icon"}),s[14]||(s[14]=t("h3",null,"日志管理",-1))]),t("div",X,[s[19]||(s[19]=t("p",{class:"card-description"},"查看和管理应用程序日志",-1)),t("div",aa,[t("div",sa,[s[15]||(s[15]=t("span",{class:"stat-label"},"日志条数:",-1)),t("span",ea,m(Fa.count),1)]),t("div",la,[s[16]||(s[16]=t("span",{class:"stat-label"},"错误数:",-1)),t("span",na,m(Fa.errors),1)])]),t("div",ta,[t("button",{onClick:Qa,class:"btn btn-outline"},[c(d(g)),s[17]||(s[17]=o(" 查看日志 "))]),t("button",{onClick:Va,class:"btn btn-outline",disabled:M.value},[c(d(y)),s[18]||(s[18]=o(" 导出日志 "))],8,ia)])])]),t("div",ca,[t("div",oa,[c(d(h),{class:"card-icon"}),s[20]||(s[20]=t("h3",null,"性能监控",-1))]),t("div",ra,[s[25]||(s[25]=t("p",{class:"card-description"},"监控应用程序性能指标",-1)),t("div",da,[t("div",ua,[s[21]||(s[21]=t("span",{class:"stat-label"},"内存使用:",-1)),t("span",va,m(Ia.memory),1)]),t("div",ma,[s[22]||(s[22]=t("span",{class:"stat-label"},"页面加载:",-1)),t("span",ba,m(Ia.loadTime),1)])]),t("div",pa,[t("button",{onClick:Ya,class:"btn btn-outline",disabled:M.value},[c(d(w)),s[23]||(s[23]=o(" 性能测试 "))],8,ga),t("button",{onClick:Xa,class:"btn btn-outline"},[c(d(S)),s[24]||(s[24]=o(" 性能报告 "))])])])])]),t("div",fa,[s[32]||(s[32]=t("h2",{class:"section-title"},"系统信息",-1)),t("div",ya,[t("div",ha,[s[26]||(s[26]=t("span",{class:"info-label"},"浏览器:",-1)),t("span",wa,m(Ua.browser),1)]),t("div",Sa,[s[27]||(s[27]=t("span",{class:"info-label"},"操作系统:",-1)),t("span",ka,m(Ua.os),1)]),t("div",Ba,[s[28]||(s[28]=t("span",{class:"info-label"},"屏幕分辨率:",-1)),t("span",Ma,m(Ua.screen),1)]),t("div",Ca,[s[29]||(s[29]=t("span",{class:"info-label"},"时区:",-1)),t("span",xa,m(Ua.timezone),1)]),t("div",La,[s[30]||(s[30]=t("span",{class:"info-label"},"语言:",-1)),t("span",Ea,m(Ua.language),1)]),t("div",Oa,[s[31]||(s[31]=t("span",{class:"info-label"},"在线状态:",-1)),t("span",{class:r(["info-value",Ua.online?"text-green-600":"text-red-600"])},m(Ua.online?"在线":"离线"),3)])])]),M.value?(B(),n("div",za,[t("div",Ta,[c(d(k),{class:"animate-spin"}),s[33]||(s[33]=t("span",null,"处理中...",-1))])])):i("",!0)]))}}),[["__scopeId","data-v-a1b6c2d0"]]);export{ja as default};
