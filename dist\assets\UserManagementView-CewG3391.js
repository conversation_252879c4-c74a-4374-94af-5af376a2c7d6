import{d as a,b as s,r as e,c as t,w as l,m as i,t as n,q as c,z as o,u as r,X as d,E as u,C as v,a3 as p,ar as m,K as f,a$ as _,b5 as b,F as g,A as h,v as y,D as k,aI as w,H as C,b1 as D,y as x,a4 as U,aT as M,b6 as T,B as S,o as I,b0 as V,aU as L,ah as Z,b7 as z,aA as A,b8 as F,R as O,a0 as $,U as j,ae as B,aQ as N,b9 as Y,G as q}from"./vendor-CnBwbdGX.js";import{_ as E}from"./index-DfMIV_22.js";const G={class:"modal-header"},H={class:"modal-body"},K={key:0,class:"user-details"},P={class:"info-section"},Q={class:"info-grid"},R={class:"info-item"},J={class:"info-value"},W={class:"info-item"},X={class:"info-value"},aa={class:"info-item"},sa={class:"info-value"},ea={class:"info-item"},ta={class:"info-item"},la={class:"info-item"},ia={class:"info-value"},na={class:"info-item"},ca={class:"info-value"},oa={class:"info-item"},ra={key:0,class:"info-section"},da={class:"info-grid"},ua={class:"info-item"},va={class:"info-value"},pa={class:"info-item"},ma={class:"avatar-container"},fa=["src","alt"],_a={key:1,class:"avatar-placeholder"},ba={class:"info-item"},ga={class:"info-value"},ha={class:"info-item"},ya={class:"info-value"},ka=["href"],wa={key:1},Ca={class:"info-item"},Da={class:"info-value"},xa={class:"info-section"},Ua={class:"stats-grid"},Ma={class:"stat-card"},Ta={class:"stat-icon"},Sa={class:"stat-content"},Ia={class:"stat-value"},Va={class:"stat-card"},La={class:"stat-icon"},Za={class:"stat-content"},za={class:"stat-value"},Aa={class:"stat-card"},Fa={class:"stat-icon"},Oa={class:"stat-content"},$a={class:"stat-value"},ja={class:"stat-card"},Ba={class:"stat-icon"},Na={class:"stat-content"},Ya={class:"stat-value"},qa={class:"info-section"},Ea={class:"activity-list"},Ga={key:0,class:"no-activity"},Ha={class:"activity-icon"},Ka={class:"activity-content"},Pa={class:"activity-description"},Qa={class:"activity-time"},Ra={key:1,class:"loading-state"},Ja={class:"modal-footer"},Wa=E(a({__name:"UserDetailModal",props:{isVisible:{type:Boolean},user:{}},emits:["close","edit"],setup(a,{emit:I}){const V=a,L=I,Z=s({favorites:0,orders:0,totalSpent:0,loginCount:0}),z=e([]);t(()=>V.isVisible&&!V.user),l(()=>V.user,a=>{a&&(O(a.id),$(a.id))},{immediate:!0});const A=()=>{L("close")},F=()=>{V.user&&L("edit",V.user)},O=async a=>{try{Z.favorites=Math.floor(50*Math.random()),Z.orders=Math.floor(20*Math.random()),Z.totalSpent=Math.floor(1e4*Math.random()),Z.loginCount=Math.floor(100*Math.random())}catch(s){console.error("加载用户统计失败:",s)}},$=async a=>{try{z.value=[{id:"1",type:"login",description:"用户登录系统",created_at:(new Date).toISOString()},{id:"2",type:"order",description:"创建了新订单",created_at:new Date(Date.now()-864e5).toISOString()},{id:"3",type:"favorite",description:"收藏了工具",created_at:new Date(Date.now()-1728e5).toISOString()}]}catch(s){console.error("加载用户活动失败:",s)}},j=a=>a?new Date(a).toLocaleString("zh-CN"):"未知",B=a=>({admin:"管理员",user:"普通用户",vip:"VIP用户"}[a]||a),N=a=>({active:"活跃",inactive:"非活跃",banned:"已封禁",pending:"待激活"}[a]||a),Y=a=>{const s={login:T,order:M,favorite:U,default:b};return s[a]||s.default};return(a,s)=>{return a.isVisible?(S(),i("div",{key:0,class:"modal-overlay",onClick:A},[c("div",{class:"modal-content",onClick:s[0]||(s[0]=x(()=>{},["stop"]))},[c("div",G,[s[1]||(s[1]=c("h2",{class:"modal-title"},"用户详情",-1)),c("button",{onClick:A,class:"close-button"},[o(r(d))])]),c("div",H,[a.user?(S(),i("div",K,[c("div",P,[s[10]||(s[10]=c("h3",{class:"section-title"},"基本信息",-1)),c("div",Q,[c("div",R,[s[2]||(s[2]=c("span",{class:"info-label"},"用户ID:",-1)),c("span",J,u(a.user.id),1)]),c("div",W,[s[3]||(s[3]=c("span",{class:"info-label"},"用户名:",-1)),c("span",X,u(a.user.username||"未设置"),1)]),c("div",aa,[s[4]||(s[4]=c("span",{class:"info-label"},"邮箱:",-1)),c("span",sa,u(a.user.email),1)]),c("div",ea,[s[5]||(s[5]=c("span",{class:"info-label"},"角色:",-1)),c("span",{class:v(["info-value",(t=a.user.role,{admin:"text-red-600 font-semibold",vip:"text-purple-600 font-semibold",user:"text-blue-600"}[t]||"text-gray-600")])},u(B(a.user.role)),3)]),c("div",ta,[s[6]||(s[6]=c("span",{class:"info-label"},"状态:",-1)),c("span",{class:v(["info-value",(e=a.user.status,{active:"text-green-600",inactive:"text-gray-600",banned:"text-red-600",pending:"text-yellow-600"}[e]||"text-gray-600")])},u(N(a.user.status)),3)]),c("div",la,[s[7]||(s[7]=c("span",{class:"info-label"},"注册时间:",-1)),c("span",ia,u(j(a.user.created_at)),1)]),c("div",na,[s[8]||(s[8]=c("span",{class:"info-label"},"最后登录:",-1)),c("span",ca,u(j(a.user.last_sign_in_at)),1)]),c("div",oa,[s[9]||(s[9]=c("span",{class:"info-label"},"邮箱验证:",-1)),c("span",{class:v(["info-value",a.user.email_confirmed_at?"text-green-600":"text-red-600"])},u(a.user.email_confirmed_at?"已验证":"未验证"),3)])])]),a.user.user_profiles?(S(),i("div",ra,[s[16]||(s[16]=c("h3",{class:"section-title"},"个人资料",-1)),c("div",da,[c("div",ua,[s[11]||(s[11]=c("span",{class:"info-label"},"显示名称:",-1)),c("span",va,u(a.user.user_profiles.display_name||"未设置"),1)]),c("div",pa,[s[12]||(s[12]=c("span",{class:"info-label"},"头像:",-1)),c("div",ma,[a.user.user_profiles.avatar_url?(S(),i("img",{key:0,src:a.user.user_profiles.avatar_url,alt:a.user.user_profiles.display_name||a.user.username,class:"user-avatar"},null,8,fa)):(S(),i("div",_a,[o(r(p))]))])]),c("div",ba,[s[13]||(s[13]=c("span",{class:"info-label"},"个人简介:",-1)),c("span",ga,u(a.user.user_profiles.bio||"未设置"),1)]),c("div",ha,[s[14]||(s[14]=c("span",{class:"info-label"},"网站:",-1)),c("span",ya,[a.user.user_profiles.website?(S(),i("a",{key:0,href:a.user.user_profiles.website,target:"_blank",class:"link"},u(a.user.user_profiles.website),9,ka)):(S(),i("span",wa,"未设置"))])]),c("div",Ca,[s[15]||(s[15]=c("span",{class:"info-label"},"位置:",-1)),c("span",Da,u(a.user.user_profiles.location||"未设置"),1)])])])):n("",!0),c("div",xa,[s[21]||(s[21]=c("h3",{class:"section-title"},"活动统计",-1)),c("div",Ua,[c("div",Ma,[c("div",Ta,[o(r(m))]),c("div",Sa,[c("div",Ia,u(Z.favorites||0),1),s[17]||(s[17]=c("div",{class:"stat-label"},"收藏工具",-1))])]),c("div",Va,[c("div",La,[o(r(f))]),c("div",Za,[c("div",za,u(Z.orders||0),1),s[18]||(s[18]=c("div",{class:"stat-label"},"订单数量",-1))])]),c("div",Aa,[c("div",Fa,[o(r(_))]),c("div",Oa,[c("div",$a,"¥"+u(Z.totalSpent||0),1),s[19]||(s[19]=c("div",{class:"stat-label"},"总消费",-1))])]),c("div",ja,[c("div",Ba,[o(r(b))]),c("div",Na,[c("div",Ya,u(Z.loginCount||0),1),s[20]||(s[20]=c("div",{class:"stat-label"},"登录次数",-1))])])])]),c("div",qa,[s[22]||(s[22]=c("h3",{class:"section-title"},"最近活动",-1)),c("div",Ea,[0===z.value.length?(S(),i("div",Ga," 暂无活动记录 ")):(S(!0),i(g,{key:1},h(z.value,a=>(S(),i("div",{key:a.id,class:"activity-item"},[c("div",Ha,[(S(),y(k(Y(a.type))))]),c("div",Ka,[c("div",Pa,u(a.description),1),c("div",Qa,u(j(a.created_at)),1)])]))),128))])])])):(S(),i("div",Ra,[o(r(w),{class:"animate-spin"}),s[23]||(s[23]=c("span",null,"加载用户信息中...",-1))]))]),c("div",Ja,[c("button",{onClick:A,class:"btn btn-secondary"},"关闭"),c("button",{onClick:F,class:"btn btn-primary"},[o(r(D)),s[24]||(s[24]=C(" 编辑用户 "))])])])])):n("",!0);var e,t}}}),[["__scopeId","data-v-2a0d9c2c"]]),Xa={class:"user-management-view"},as={class:"page-header"},ss={class:"header-actions"},es={class:"stats-grid"},ts={class:"stat-card"},ls={class:"stat-icon"},is={class:"stat-content"},ns={class:"stat-card"},cs={class:"stat-icon active"},os={class:"stat-content"},rs={class:"stat-card"},ds={class:"stat-icon admin"},us={class:"stat-content"},vs={class:"stat-card"},ps={class:"stat-icon new"},ms={class:"stat-content"},fs={class:"filters-section"},_s={class:"search-box"},bs={class:"filter-controls"},gs={class:"users-table-container"},hs={key:0,class:"loading-state"},ys={key:1,class:"users-table"},ks={class:"user-info"},ws={class:"user-avatar"},Cs=["src","alt"],Ds={class:"user-details"},xs={key:0,class:"username"},Us={class:"actions"},Ms=["onClick"],Ts=["onClick"],Ss=["onClick"],Is={class:"pagination"},Vs=["disabled"],Ls={class:"pagination-info"},Zs=["disabled"],zs=E(a({__name:"UserManagementView",setup(a){const l=e(!1),d=e([]),m=e(null),f=e(1),_=e(10),b=s({search:"",role:"",status:"",sort:"created_at_desc"}),k=t(()=>({totalUsers:d.value.length,activeUsers:d.value.filter(a=>a.is_active).length,adminUsers:d.value.filter(a=>"admin"===a.role||"super_admin"===a.role).length,newUsersThisMonth:d.value.filter(a=>{const s=new Date(a.created_at),e=new Date;return s.getMonth()===e.getMonth()&&s.getFullYear()===e.getFullYear()}).length})),x=t(()=>{let a=[...d.value];if(b.search){const s=b.search.toLowerCase();a=a.filter(a=>{var e,t;return a.email.toLowerCase().includes(s)||(null==(e=a.full_name)?void 0:e.toLowerCase().includes(s))||(null==(t=a.username)?void 0:t.toLowerCase().includes(s))})}if(b.role&&(a=a.filter(a=>a.role===b.role)),b.status){const s="active"===b.status;a=a.filter(a=>a.is_active===s)}return a.sort((a,s)=>{switch(b.sort){case"created_at_desc":return new Date(s.created_at).getTime()-new Date(a.created_at).getTime();case"created_at_asc":return new Date(a.created_at).getTime()-new Date(s.created_at).getTime();case"last_login_desc":return a.last_login_at?s.last_login_at?new Date(s.last_login_at).getTime()-new Date(a.last_login_at).getTime():-1:1;case"name_asc":return(a.full_name||a.email).localeCompare(s.full_name||s.email);default:return 0}}),a}),U=t(()=>Math.ceil(x.value.length/_.value)),M=t(()=>{const a=(f.value-1)*_.value,s=a+_.value;return x.value.slice(a,s)}),T=async()=>{try{l.value=!0,d.value=[{id:"1",email:"<EMAIL>",full_name:"系统管理员",username:"admin",role:"super_admin",is_active:!0,created_at:"2024-01-01T00:00:00Z",last_login_at:"2024-06-30T12:00:00Z"},{id:"2",email:"<EMAIL>",full_name:"普通用户",username:"user1",role:"user",is_active:!0,created_at:"2024-06-01T00:00:00Z",last_login_at:"2024-06-29T10:00:00Z"}]}catch(a){console.error("加载用户数据失败:",a)}finally{l.value=!1}},E=()=>{T()},G=()=>{console.log("导出用户数据")},H=a=>{const s=d.value.findIndex(s=>s.id===a.id);-1!==s&&(d.value[s]=a)},K=a=>{switch(a){case"super_admin":return"超级管理员";case"admin":return"管理员";case"user":return"普通用户";default:return"未知"}},P=a=>new Date(a).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"});return I(()=>{T()}),(a,s)=>(S(),i("div",Xa,[c("div",as,[s[9]||(s[9]=c("div",null,[c("h1",null,"用户管理"),c("p",null,"管理系统用户，查看用户信息和活动状态")],-1)),c("div",ss,[c("button",{onClick:E,class:"btn btn-secondary"},[o(r(V),{class:"icon"}),s[7]||(s[7]=C(" 刷新 "))]),c("button",{onClick:G,class:"btn btn-primary"},[o(r(L),{class:"icon"}),s[8]||(s[8]=C(" 导出用户 "))])])]),c("div",es,[c("div",ts,[c("div",ls,[o(r(Z))]),c("div",is,[c("h3",null,u(k.value.totalUsers),1),s[10]||(s[10]=c("p",null,"总用户数",-1))])]),c("div",ns,[c("div",cs,[o(r(z))]),c("div",os,[c("h3",null,u(k.value.activeUsers),1),s[11]||(s[11]=c("p",null,"活跃用户",-1))])]),c("div",rs,[c("div",ds,[o(r(A))]),c("div",us,[c("h3",null,u(k.value.adminUsers),1),s[12]||(s[12]=c("p",null,"管理员",-1))])]),c("div",vs,[c("div",ps,[o(r(F))]),c("div",ms,[c("h3",null,u(k.value.newUsersThisMonth),1),s[13]||(s[13]=c("p",null,"本月新增",-1))])])]),c("div",fs,[c("div",_s,[o(r($),{class:"search-icon"}),O(c("input",{"onUpdate:modelValue":s[0]||(s[0]=a=>b.search=a),type:"text",placeholder:"搜索用户名、邮箱...",class:"search-input"},null,512),[[j,b.search]])]),c("div",bs,[O(c("select",{"onUpdate:modelValue":s[1]||(s[1]=a=>b.role=a),class:"filter-select"},s[14]||(s[14]=[c("option",{value:""},"所有角色",-1),c("option",{value:"user"},"普通用户",-1),c("option",{value:"admin"},"管理员",-1),c("option",{value:"super_admin"},"超级管理员",-1)]),512),[[B,b.role]]),O(c("select",{"onUpdate:modelValue":s[2]||(s[2]=a=>b.status=a),class:"filter-select"},s[15]||(s[15]=[c("option",{value:""},"所有状态",-1),c("option",{value:"active"},"活跃",-1),c("option",{value:"inactive"},"非活跃",-1)]),512),[[B,b.status]]),O(c("select",{"onUpdate:modelValue":s[3]||(s[3]=a=>b.sort=a),class:"filter-select"},s[16]||(s[16]=[c("option",{value:"created_at_desc"},"最新注册",-1),c("option",{value:"created_at_asc"},"最早注册",-1),c("option",{value:"last_login_desc"},"最近登录",-1),c("option",{value:"name_asc"},"姓名 A-Z",-1)]),512),[[B,b.sort]])])]),c("div",gs,[l.value?(S(),i("div",hs,[o(r(w),{class:"loading-icon"}),s[17]||(s[17]=c("p",null,"加载用户数据中...",-1))])):(S(),i("table",ys,[s[18]||(s[18]=c("thead",null,[c("tr",null,[c("th",null,"用户信息"),c("th",null,"角色"),c("th",null,"状态"),c("th",null,"注册时间"),c("th",null,"最后登录"),c("th",null,"操作")])],-1)),c("tbody",null,[(S(!0),i(g,null,h(M.value,a=>(S(),i("tr",{key:a.id,class:"user-row"},[c("td",ks,[c("div",ws,[a.avatar_url?(S(),i("img",{key:0,src:a.avatar_url,alt:a.full_name||a.email},null,8,Cs)):(S(),y(r(p),{key:1}))]),c("div",Ds,[c("h4",null,u(a.full_name||"未设置姓名"),1),c("p",null,u(a.email),1),a.username?(S(),i("span",xs,"@"+u(a.username),1)):n("",!0)])]),c("td",null,[c("span",{class:v(["role-badge",a.role])},u(K(a.role)),3)]),c("td",null,[c("span",{class:v(["status-badge",a.is_active?"active":"inactive"])},u(a.is_active?"活跃":"非活跃"),3)]),c("td",null,u(P(a.created_at)),1),c("td",null,u(a.last_login_at?P(a.last_login_at):"从未登录"),1),c("td",Us,[c("button",{onClick:s=>(a=>{m.value=a})(a),class:"action-btn view"},[o(r(N))],8,Ms),c("button",{onClick:s=>(a=>{m.value=a})(a),class:"action-btn edit"},[o(r(D))],8,Ts),"super_admin"!==a.role?(S(),i("button",{key:0,onClick:s=>(async a=>{try{a.is_active=!a.is_active,console.log(`用户 ${a.email} 状态已${a.is_active?"激活":"禁用"}`)}catch(s){console.error("切换用户状态失败:",s)}})(a),class:v(["action-btn",a.is_active?"disable":"enable"])},[a.is_active?(S(),y(r(Y),{key:0})):(S(),y(r(q),{key:1}))],10,Ss)):n("",!0)])]))),128))])]))]),c("div",Is,[c("button",{onClick:s[4]||(s[4]=a=>f.value--),disabled:1===f.value,class:"pagination-btn"}," 上一页 ",8,Vs),c("span",Ls," 第 "+u(f.value)+" 页，共 "+u(U.value)+" 页 ",1),c("button",{onClick:s[5]||(s[5]=a=>f.value++),disabled:f.value===U.value,class:"pagination-btn"}," 下一页 ",8,Zs)]),m.value?(S(),y(Wa,{key:0,user:m.value,onClose:s[6]||(s[6]=a=>m.value=null),onUpdated:H},null,8,["user"])):n("",!0)]))}}),[["__scopeId","data-v-3603e48c"]]);export{zs as default};
