import{d as l,r as e,o as a,m as u,q as t,t as o,E as n,R as s,U as v,B as i}from"./vendor-CnBwbdGX.js";import{_ as r}from"./index-DfMIV_22.js";const c={class:"test-settings"},p={class:"current-values"},d={class:"form-section"},m={class:"form-group"},g={class:"form-group"},b={class:"form-group"},f={class:"form-group"},y={class:"form-group"},h={class:"form-group"},C={class:"form-group"},U={class:"form-group"},V={class:"form-group"},S={class:"form-group"},k={key:0,class:"message"},x=r(l({__name:"TestSettingsView",setup(l){const r=e("工具导航站"),x=e("专注于为用户提供优质的工具导航和产品展示服务，致力于提升工作效率，让每个人都能找到最适合的工具和产品。"),I=e("<EMAIL>"),N=e("+86 138-0000-0000"),_=e("© 2024 工具导航站. 保留所有权利."),J=e(1e3),O=e(50),j=e(1e4),w=e("https://github.com/jiayuwee/advanced-tools-navigation"),E=e("<EMAIL>"),T=e(""),$=()=>{try{const l={name:r.value,description:x.value,contact:{email:I.value,phone:N.value}},e={copyright:_.value,stats:{toolsCount:J.value,categoriesCount:O.value,usersCount:j.value},social:{github:w.value,email:E.value}};localStorage.setItem("siteConfig",JSON.stringify(l)),localStorage.setItem("footerConfig",JSON.stringify(e)),T.value="设置保存成功！",setTimeout(()=>{T.value=""},3e3)}catch(l){console.error("保存失败:",l),T.value="保存失败，请重试"}},q=()=>{var l,e,a,u,t,o,n;try{const s=localStorage.getItem("siteConfig"),v=localStorage.getItem("footerConfig");if(s){const a=JSON.parse(s);r.value=a.name||r.value,x.value=a.description||x.value,I.value=(null==(l=a.contact)?void 0:l.email)||I.value,N.value=(null==(e=a.contact)?void 0:e.phone)||N.value}if(v){const l=JSON.parse(v);_.value=l.copyright||_.value,J.value=(null==(a=l.stats)?void 0:a.toolsCount)||J.value,O.value=(null==(u=l.stats)?void 0:u.categoriesCount)||O.value,j.value=(null==(t=l.stats)?void 0:t.usersCount)||j.value,w.value=(null==(o=l.social)?void 0:o.github)||w.value,E.value=(null==(n=l.social)?void 0:n.email)||E.value}T.value="设置加载成功！",setTimeout(()=>{T.value=""},3e3)}catch(s){console.error("加载失败:",s),T.value="加载失败"}},B=()=>{const l={siteName:r.value,siteDescription:x.value,contactEmail:I.value,contactPhone:N.value,copyright:_.value,toolsCount:J.value,categoriesCount:O.value,usersCount:j.value,githubLink:w.value,socialEmail:E.value};console.log("当前数据:",l),alert(`当前数据:\n网站名称: ${r.value}\n联系邮箱: ${I.value}\n版权信息: ${_.value}`)};return a(()=>{q()}),(l,e)=>(i(),u("div",c,[e[22]||(e[22]=t("h1",null,"测试设置页面",-1)),t("div",p,[e[10]||(e[10]=t("h2",null,"当前值显示",-1)),t("p",null,"网站名称: "+n(r.value),1),t("p",null,"网站描述: "+n(x.value),1),t("p",null,"联系邮箱: "+n(I.value),1),t("p",null,"联系电话: "+n(N.value),1),t("p",null,"版权信息: "+n(_.value),1)]),t("div",d,[e[21]||(e[21]=t("h2",null,"修改设置",-1)),t("div",m,[e[11]||(e[11]=t("label",null,"网站名称:",-1)),s(t("input",{"onUpdate:modelValue":e[0]||(e[0]=l=>r.value=l),type:"text"},null,512),[[v,r.value]])]),t("div",g,[e[12]||(e[12]=t("label",null,"网站描述:",-1)),s(t("textarea",{"onUpdate:modelValue":e[1]||(e[1]=l=>x.value=l),rows:"3"},null,512),[[v,x.value]])]),t("div",b,[e[13]||(e[13]=t("label",null,"联系邮箱:",-1)),s(t("input",{"onUpdate:modelValue":e[2]||(e[2]=l=>I.value=l),type:"email"},null,512),[[v,I.value]])]),t("div",f,[e[14]||(e[14]=t("label",null,"联系电话:",-1)),s(t("input",{"onUpdate:modelValue":e[3]||(e[3]=l=>N.value=l),type:"tel"},null,512),[[v,N.value]])]),t("div",y,[e[15]||(e[15]=t("label",null,"版权信息:",-1)),s(t("input",{"onUpdate:modelValue":e[4]||(e[4]=l=>_.value=l),type:"text"},null,512),[[v,_.value]])]),t("div",h,[e[16]||(e[16]=t("label",null,"精选工具数量:",-1)),s(t("input",{"onUpdate:modelValue":e[5]||(e[5]=l=>J.value=l),type:"number"},null,512),[[v,J.value,void 0,{number:!0}]])]),t("div",C,[e[17]||(e[17]=t("label",null,"工具分类数量:",-1)),s(t("input",{"onUpdate:modelValue":e[6]||(e[6]=l=>O.value=l),type:"number"},null,512),[[v,O.value,void 0,{number:!0}]])]),t("div",U,[e[18]||(e[18]=t("label",null,"用户使用数量:",-1)),s(t("input",{"onUpdate:modelValue":e[7]||(e[7]=l=>j.value=l),type:"number"},null,512),[[v,j.value,void 0,{number:!0}]])]),t("div",V,[e[19]||(e[19]=t("label",null,"GitHub 链接:",-1)),s(t("input",{"onUpdate:modelValue":e[8]||(e[8]=l=>w.value=l),type:"url"},null,512),[[v,w.value]])]),t("div",S,[e[20]||(e[20]=t("label",null,"社交邮箱:",-1)),s(t("input",{"onUpdate:modelValue":e[9]||(e[9]=l=>E.value=l),type:"email"},null,512),[[v,E.value]])])]),t("div",{class:"actions"},[t("button",{onClick:$,class:"save-btn"},"保存所有设置"),t("button",{onClick:q,class:"load-btn"},"重新加载"),t("button",{onClick:B,class:"test-btn"},"显示当前数据")]),T.value?(i(),u("div",k,n(T.value),1)):o("",!0)]))}}),[["__scopeId","data-v-da3d9101"]]);export{x as default};
