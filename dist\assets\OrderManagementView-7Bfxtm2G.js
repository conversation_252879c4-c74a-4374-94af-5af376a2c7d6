import{d as a,r as t,b as e,c as s,w as l,o as n,m as o,q as c,z as d,H as i,u as r,b0 as u,aU as p,aY as v,E as h,Q as b,ad as m,a$ as g,R as y,a0 as f,U as w,ae as k,a6 as R,aI as O,F as C,A as _,C as S,t as U,aQ as D,ba as M,X as x,B as V}from"./vendor-CnBwbdGX.js";import{OrderService as A}from"./orderService-BKP3fvOY.js";import{_ as I}from"./index-DfMIV_22.js";const L={class:"order-management-view"},$={class:"page-header"},j={class:"header-actions"},E={class:"stats-grid"},N={class:"stat-card"},z={class:"stat-icon"},B={class:"stat-content"},J={class:"stat-card"},Q={class:"stat-icon completed"},q={class:"stat-content"},F={class:"stat-card"},H={class:"stat-icon pending"},T={class:"stat-content"},Y={class:"stat-card"},G={class:"stat-icon revenue"},K={class:"stat-content"},P={class:"filters-section"},W={class:"search-box"},X={class:"filter-controls"},Z={class:"orders-table-container"},aa={key:0,class:"loading-state"},ta={key:1,class:"orders-table"},ea={class:"order-info"},sa={class:"order-details"},la={class:"user-info"},na={class:"user-details"},oa={class:"amount"},ca={class:"price"},da={class:"currency"},ia={class:"actions"},ra=["onClick"],ua=["onClick"],pa=["onClick"],va={class:"pagination"},ha=["disabled"],ba={class:"pagination-info"},ma=["disabled"],ga=I(a({__name:"OrderManagementView",setup(a){const I=t(!1),ga=t([]),ya=t(0),fa=t(1),wa=t(10),ka=e({search:"",status:"",paymentMethod:"",dateRange:{start:"",end:""}}),Ra=s(()=>({totalOrders:ga.value.length,completedOrders:ga.value.filter(a=>"completed"===a.status).length,pendingOrders:ga.value.filter(a=>"pending"===a.status).length,totalRevenue:ga.value.filter(a=>"completed"===a.status).reduce((a,t)=>a+t.total_amount,0)})),Oa=s(()=>Math.ceil(ya.value/wa.value)),Ca=s(()=>ga.value),_a=async()=>{try{I.value=!0;const a={search:ka.search||void 0,status:ka.status||void 0,paymentMethod:ka.paymentMethod||void 0,startDate:ka.dateRange.start||void 0,endDate:ka.dateRange.end||void 0,page:fa.value,limit:wa.value},t=await A.getAllOrders(a);ga.value=t.orders,ya.value=t.total}catch(a){console.error("加载订单数据失败:",a),alert("加载订单数据失败，请稍后重试")}finally{I.value=!1}},Sa=()=>{_a()},Ua=async()=>{try{const a={status:ka.status||void 0,startDate:ka.dateRange.start||void 0,endDate:ka.dateRange.end||void 0},t=await A.exportOrders(a),e=new Blob([t],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a"),l=URL.createObjectURL(e);s.setAttribute("href",l),s.setAttribute("download",`orders_${(new Date).toISOString().split("T")[0]}.csv`),s.style.visibility="hidden",document.body.appendChild(s),s.click(),document.body.removeChild(s),console.log("订单数据导出成功")}catch(a){console.error("导出订单数据失败:",a),alert("导出订单数据失败，请稍后重试")}},Da=a=>{switch(a){case"pending":return"待支付";case"paid":return"已支付";case"cancelled":return"已取消";case"refunded":return"已退款";default:return"未知"}},Ma=a=>{switch(a){case"alipay":return"支付宝";case"wechat":return"微信支付";case"credit_card":return"信用卡";default:return"未知"}};return l(()=>[ka.search,ka.status,ka.paymentMethod,ka.dateRange.start,ka.dateRange.end],()=>{fa.value=1,_a()},{deep:!0}),l([fa,wa],()=>{_a()}),n(()=>{_a()}),(a,t)=>(V(),o("div",L,[c("div",$,[t[9]||(t[9]=c("div",null,[c("h1",null,"订单管理"),c("p",null,"管理系统订单，查看订单状态和支付信息")],-1)),c("div",j,[c("button",{onClick:Sa,class:"btn btn-secondary"},[d(r(u),{class:"icon"}),t[7]||(t[7]=i(" 刷新 "))]),c("button",{onClick:Ua,class:"btn btn-primary"},[d(r(p),{class:"icon"}),t[8]||(t[8]=i(" 导出订单 "))])])]),c("div",E,[c("div",N,[c("div",z,[d(r(v))]),c("div",B,[c("h3",null,h(Ra.value.totalOrders),1),t[10]||(t[10]=c("p",null,"总订单数",-1))])]),c("div",J,[c("div",Q,[d(r(b))]),c("div",q,[c("h3",null,h(Ra.value.completedOrders),1),t[11]||(t[11]=c("p",null,"已完成",-1))])]),c("div",F,[c("div",H,[d(r(m))]),c("div",T,[c("h3",null,h(Ra.value.pendingOrders),1),t[12]||(t[12]=c("p",null,"待处理",-1))])]),c("div",Y,[c("div",G,[d(r(g))]),c("div",K,[c("h3",null,"¥"+h(Ra.value.totalRevenue.toLocaleString()),1),t[13]||(t[13]=c("p",null,"总收入",-1))])])]),c("div",P,[c("div",W,[d(r(f),{class:"search-icon"}),y(c("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>ka.search=a),type:"text",placeholder:"搜索订单号、用户邮箱...",class:"search-input"},null,512),[[w,ka.search]])]),c("div",X,[y(c("select",{"onUpdate:modelValue":t[1]||(t[1]=a=>ka.status=a),class:"filter-select"},t[14]||(t[14]=[R('<option value="" data-v-4bc5bbca>所有状态</option><option value="pending" data-v-4bc5bbca>待处理</option><option value="processing" data-v-4bc5bbca>处理中</option><option value="completed" data-v-4bc5bbca>已完成</option><option value="cancelled" data-v-4bc5bbca>已取消</option>',5)]),512),[[k,ka.status]]),y(c("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>ka.paymentMethod=a),class:"filter-select"},t[15]||(t[15]=[c("option",{value:""},"所有支付方式",-1),c("option",{value:"alipay"},"支付宝",-1),c("option",{value:"wechat"},"微信支付",-1),c("option",{value:"credit_card"},"信用卡",-1)]),512),[[k,ka.paymentMethod]]),y(c("input",{"onUpdate:modelValue":t[3]||(t[3]=a=>ka.dateRange.start=a),type:"date",class:"filter-select",placeholder:"开始日期"},null,512),[[w,ka.dateRange.start]]),y(c("input",{"onUpdate:modelValue":t[4]||(t[4]=a=>ka.dateRange.end=a),type:"date",class:"filter-select",placeholder:"结束日期"},null,512),[[w,ka.dateRange.end]])])]),c("div",Z,[I.value?(V(),o("div",aa,[d(r(O),{class:"loading-icon"}),t[16]||(t[16]=c("p",null,"加载订单数据中...",-1))])):(V(),o("table",ta,[t[17]||(t[17]=c("thead",null,[c("tr",null,[c("th",null,"订单信息"),c("th",null,"用户"),c("th",null,"金额"),c("th",null,"支付方式"),c("th",null,"状态"),c("th",null,"创建时间"),c("th",null,"操作")])],-1)),c("tbody",null,[(V(!0),o(C,null,_(Ca.value,a=>{var t,e,s,l;return V(),o("tr",{key:a.id,class:"order-row"},[c("td",ea,[c("div",sa,[c("h4",null,"#"+h(a.id.slice(0,8)),1),c("p",null,h((null==(t=a.items)?void 0:t.length)||0)+" 个商品",1)])]),c("td",la,[c("div",na,[c("h4",null,h((null==(e=a.user)?void 0:e.full_name)||"未知用户"),1),c("p",null,h((null==(s=a.user)?void 0:s.email)||"N/A"),1)])]),c("td",oa,[c("span",ca,"¥"+h(a.total_amount.toLocaleString()),1),c("span",da,h(a.currency),1)]),c("td",null,[c("span",{class:S(["payment-method",a.payment_method])},h(Ma(a.payment_method)),3)]),c("td",null,[c("span",{class:S(["status-badge",a.status])},h(Da(a.status)),3)]),c("td",null,h((l=a.created_at,new Date(l).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}))),1),c("td",ia,[c("button",{onClick:t=>(a=>{console.log("查看订单:",a.id)})(a),class:"action-btn view"},[d(r(D))],8,ra),"pending"===a.status?(V(),o("button",{key:0,onClick:t=>(async a=>{try{const t=JSON.parse(localStorage.getItem("user")||"{}");if(!t.id)throw new Error("未找到管理员用户信息");await A.updateOrderStatus(a.id,"paid",t.id),await _a(),console.log(`订单 ${a.id} 已标记为已支付`)}catch(t){console.error("处理订单失败:",t),alert("处理订单失败，请稍后重试")}})(a),class:"action-btn process"},[d(r(M))],8,ua)):U("",!0),"cancelled"!==a.status&&"completed"!==a.status?(V(),o("button",{key:1,onClick:t=>(async a=>{try{const t=JSON.parse(localStorage.getItem("user")||"{}");if(!t.id)throw new Error("未找到管理员用户信息");if(!confirm(`确定要取消订单 ${a.id} 吗？`))return;await A.updateOrderStatus(a.id,"cancelled",t.id),await _a(),console.log(`订单 ${a.id} 已取消`)}catch(t){console.error("取消订单失败:",t),alert("取消订单失败，请稍后重试")}})(a),class:"action-btn cancel"},[d(r(x))],8,pa)):U("",!0)])])}),128))])]))]),c("div",va,[c("button",{onClick:t[5]||(t[5]=a=>fa.value--),disabled:1===fa.value,class:"pagination-btn"}," 上一页 ",8,ha),c("span",ba," 第 "+h(fa.value)+" 页，共 "+h(Oa.value)+" 页 ",1),c("button",{onClick:t[6]||(t[6]=a=>fa.value++),disabled:fa.value===Oa.value,class:"pagination-btn"}," 下一页 ",8,ma)])]))}}),[["__scopeId","data-v-4bc5bbca"]]);export{ga as default};
