import{d as e,r as a,c as s,m as l,q as r,t,R as o,U as d,aV as u,v as i,u as n,aQ as c,aW as v,af as p,C as m,E as f,H as w,T as g,y as h,z as b,$ as y,_ as k,B as x}from"./vendor-CnBwbdGX.js";import{c as T,_ as P}from"./index-DfMIV_22.js";const _={class:"register-view"},V={class:"form-group"},U=["disabled"],q={class:"form-group"},N=["disabled"],z={class:"form-group"},j={class:"password-input"},A=["type","disabled"],C={class:"password-strength"},E={class:"strength-bar"},R={class:"strength-text"},Z={class:"form-group"},B=["disabled"],H={key:0,class:"error-hint"},I={class:"form-group"},Q={class:"checkbox-label"},S=["disabled"],W={key:0,class:"loading-spinner"},$={key:0,class:"error-message"},D={class:"register-footer"},F=P(e({__name:"RegisterView",setup(e){const P=T(),F=a(!1),G=a(null),J=a(!1),K=a({fullName:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1}),L=s(()=>{const e=K.value.password;if(!e)return{class:"",width:"0%",text:""};let a=0;return e.length>=8&&a++,/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^A-Za-z0-9]/.test(e)&&a++,a<2?{class:"weak",width:"20%",text:"弱"}:a<3?{class:"fair",width:"40%",text:"一般"}:a<4?{class:"good",width:"60%",text:"良好"}:a<5?{class:"strong",width:"80%",text:"强"}:{class:"very-strong",width:"100%",text:"很强"}}),M=s(()=>K.value.email&&K.value.password&&K.value.password===K.value.confirmPassword&&K.value.agreeToTerms&&K.value.password.length>=8),O=async()=>{try{F.value=!0,G.value=null,await new Promise(e=>setTimeout(e,1500)),console.log("注册成功:",K.value.email),P.push("/auth/login")}catch(e){G.value=e instanceof Error?e.message:"注册失败，请重试"}finally{F.value=!1}};return(e,a)=>{const s=k("router-link");return x(),l("div",_,[a[17]||(a[17]=r("div",{class:"register-header"},[r("h1",null,"注册"),r("p",null,"创建您的账户，开始使用工具导航站")],-1)),r("form",{class:"register-form",onSubmit:h(O,["prevent"])},[r("div",V,[a[6]||(a[6]=r("label",{for:"fullName"},"姓名",-1)),o(r("input",{id:"fullName","onUpdate:modelValue":a[0]||(a[0]=e=>K.value.fullName=e),type:"text",placeholder:"请输入您的姓名",disabled:F.value},null,8,U),[[d,K.value.fullName]])]),r("div",q,[a[7]||(a[7]=r("label",{for:"email"},"邮箱地址 *",-1)),o(r("input",{id:"email","onUpdate:modelValue":a[1]||(a[1]=e=>K.value.email=e),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:F.value},null,8,N),[[d,K.value.email]])]),r("div",z,[a[8]||(a[8]=r("label",{for:"password"},"密码 *",-1)),r("div",j,[o(r("input",{id:"password","onUpdate:modelValue":a[2]||(a[2]=e=>K.value.password=e),type:J.value?"text":"password",required:"",placeholder:"请输入密码（至少8位）",disabled:F.value},null,8,A),[[u,K.value.password]]),r("button",{type:"button",class:"password-toggle",onClick:a[3]||(a[3]=e=>J.value=!J.value)},[J.value?(x(),i(n(v),{key:1,class:"icon"})):(x(),i(n(c),{key:0,class:"icon"}))])]),r("div",C,[r("div",E,[r("div",{class:m(["strength-fill",L.value.class]),style:p({width:L.value.width})},null,6)]),r("span",R,f(L.value.text),1)])]),r("div",Z,[a[9]||(a[9]=r("label",{for:"confirmPassword"},"确认密码 *",-1)),o(r("input",{id:"confirmPassword","onUpdate:modelValue":a[4]||(a[4]=e=>K.value.confirmPassword=e),type:"password",required:"",placeholder:"请再次输入密码",disabled:F.value},null,8,B),[[d,K.value.confirmPassword]]),K.value.confirmPassword&&K.value.password!==K.value.confirmPassword?(x(),l("div",H," 密码不匹配 ")):t("",!0)]),r("div",I,[r("label",Q,[o(r("input",{"onUpdate:modelValue":a[5]||(a[5]=e=>K.value.agreeToTerms=e),type:"checkbox",required:""},null,512),[[g,K.value.agreeToTerms]]),a[10]||(a[10]=r("span",{class:"checkmark"},null,-1)),a[11]||(a[11]=w(" 我已阅读并同意 ")),a[12]||(a[12]=r("a",{href:"#",class:"terms-link"},"服务条款",-1)),a[13]||(a[13]=w(" 和 ")),a[14]||(a[14]=r("a",{href:"#",class:"terms-link"},"隐私政策",-1))])]),r("button",{type:"submit",class:"register-btn",disabled:F.value||!M.value},[F.value?(x(),l("div",W)):t("",!0),r("span",null,f(F.value?"注册中...":"注册"),1)],8,S),G.value?(x(),l("div",$,f(G.value),1)):t("",!0)],32),r("div",D,[r("p",null,[a[16]||(a[16]=w(" 已有账户？ ")),b(s,{to:"/auth/login",class:"login-link"},{default:y(()=>a[15]||(a[15]=[w("立即登录")])),_:1,__:[15]})])])])}}}),[["__scopeId","data-v-cace2648"]]);export{F as default};
