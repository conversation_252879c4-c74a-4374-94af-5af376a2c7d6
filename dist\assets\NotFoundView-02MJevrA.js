import{c as s,_ as a}from"./index-DfMIV_22.js";import{d as e,m as t,q as i,a6 as l,z as n,$ as o,H as d,u as c,a_ as v,_ as u,aR as r,ai as g,aT as m,a3 as _,B as f}from"./vendor-CnBwbdGX.js";const p={class:"not-found-view"},b={class:"not-found-container"},h={class:"not-found-content"},k={class:"error-actions"},w={class:"suggestions"},y={class:"suggestion-links"},j=a(e({__name:"NotFoundView",setup(a){const e=s(),j=()=>{window.history.length>1?e.go(-1):e.push("/")};return(s,a)=>{const e=u("router-link");return f(),t("div",p,[i("div",b,[i("div",h,[a[6]||(a[6]=i("div",{class:"error-code"},"404",-1)),a[7]||(a[7]=i("h1",{class:"error-title"},"页面未找到",-1)),a[8]||(a[8]=i("p",{class:"error-message"},"抱歉，您访问的页面不存在或已被移除",-1)),i("div",k,[n(e,{to:"/",class:"btn btn-primary"},{default:o(()=>[n(c(v),{class:"icon"}),a[0]||(a[0]=d(" 返回首页 "))]),_:1,__:[0]}),i("button",{class:"btn btn-secondary",onClick:j},[n(c(r),{class:"icon"}),a[1]||(a[1]=d(" 返回上页 "))])]),i("div",w,[a[5]||(a[5]=i("h3",null,"您可能在寻找：",-1)),i("div",y,[n(e,{to:"/tools",class:"suggestion-item"},{default:o(()=>[n(c(g),{class:"suggestion-icon"}),a[2]||(a[2]=i("div",null,[i("div",{class:"suggestion-title"},"工具导航"),i("div",{class:"suggestion-desc"},"发现实用工具")],-1))]),_:1,__:[2]}),n(e,{to:"/products",class:"suggestion-item"},{default:o(()=>[n(c(m),{class:"suggestion-icon"}),a[3]||(a[3]=i("div",null,[i("div",{class:"suggestion-title"},"产品展示"),i("div",{class:"suggestion-desc"},"浏览优质产品")],-1))]),_:1,__:[3]}),n(e,{to:"/user/profile",class:"suggestion-item"},{default:o(()=>[n(c(_),{class:"suggestion-icon"}),a[4]||(a[4]=i("div",null,[i("div",{class:"suggestion-title"},"个人中心"),i("div",{class:"suggestion-desc"},"管理您的账户")],-1))]),_:1,__:[4]})])])]),a[9]||(a[9]=l('<div class="not-found-illustration" data-v-949840e1><div class="illustration-content" data-v-949840e1><div class="floating-elements" data-v-949840e1><div class="element element-1" data-v-949840e1>🔍</div><div class="element element-2" data-v-949840e1>📄</div><div class="element element-3" data-v-949840e1>❓</div><div class="element element-4" data-v-949840e1>🚀</div></div><div class="main-illustration" data-v-949840e1><div class="search-icon" data-v-949840e1>🔍</div><div class="broken-link" data-v-949840e1>🔗</div></div></div></div>',1))])])}}}),[["__scopeId","data-v-949840e1"]]);export{j as default};
