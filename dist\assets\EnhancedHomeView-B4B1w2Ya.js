import{d as a,r as s,c as l,o as t,m as e,q as i,z as o,$ as n,H as c,u as r,aq as d,_ as u,a4 as v,P as h,F as p,A as m,C as g,E as f,t as k,af as y,y as _,ar as C,B as b}from"./vendor-CnBwbdGX.js";import{u as w,a as z,b as x,c as E,_ as j}from"./index-DfMIV_22.js";import{E as F}from"./EnhancedSearchBox-CEYnlaFY.js";const L={class:"enhanced-home-view"},R={class:"hero-section"},U={class:"container"},q={class:"hero-content"},A={class:"search-section"},B={class:"main-content"},H={class:"container"},S={class:"content-layout"},W={class:"sidebar"},I={class:"sidebar-section"},P={class:"nav-links"},V={class:"sidebar-section"},$={class:"category-filters"},D=["onClick"],G={class:"tools-grid"},J=["onClick"],K=["onClick"],M={class:"tool-header"},N={class:"tool-icon"},O={class:"tool-title"},Q={class:"tool-subtitle"},T={class:"tool-body"},X={class:"tool-description"},Y={class:"tool-tags"},Z={key:0,class:"tool-tag more"},aa={key:0,class:"empty-state"},sa=j(a({__name:"EnhancedHomeView",setup(a){const j=w(),sa=z(),la=x(),ta=E(),ea=s(null),ia=s(null),oa=l(()=>sa.categories),na=l(()=>{if(ia.value)return ia.value.items||[];let a=j.tools;return ea.value&&(a=a.filter(a=>a.category_id===ea.value)),a}),ca=a=>{ia.value=a},ra=()=>{ia.value=null},da=()=>{ia.value=null,ea.value=null};return t(async()=>{j.initialized||await j.initialize(),sa.initialized||await sa.initialize()}),(a,s)=>{const l=u("router-link");return b(),e("div",L,[i("header",R,[i("div",U,[i("div",q,[s[0]||(s[0]=i("div",{class:"logo-section"},[i("div",{class:"logo-icon"},"🚀"),i("h1",{class:"main-title"},"高效工具导航站")],-1)),s[1]||(s[1]=i("p",{class:"tagline"},"精心挑选的优质工具，让您的工作效率倍增",-1)),i("div",A,[o(F,{placeholder:"搜索工具、分类或功能...","auto-focus":!1,onSearch:ca,onClear:ra})])])])]),i("main",B,[i("div",H,[i("div",S,[i("aside",W,[i("div",I,[s[5]||(s[5]=i("h3",null,"导航",-1)),i("nav",P,[o(l,{to:"/tools",class:"nav-link active"},{default:n(()=>[o(r(d),{class:"icon"}),s[2]||(s[2]=c(" 全部工具 "))]),_:1,__:[2]}),o(l,{to:"/favorites",class:"nav-link"},{default:n(()=>[o(r(v),{class:"icon"}),s[3]||(s[3]=c(" 我的收藏 "))]),_:1,__:[3]}),o(l,{to:"/products",class:"nav-link"},{default:n(()=>[o(r(h),{class:"icon"}),s[4]||(s[4]=c(" 我的产品 "))]),_:1,__:[4]})])]),i("div",V,[s[6]||(s[6]=i("h3",null,"分类",-1)),i("div",$,[(b(!0),e(p,null,m(oa.value,a=>(b(),e("button",{key:a.id,class:g(["category-tag",{active:ea.value===a.id}]),onClick:s=>{return l=a.id,void(ea.value=ea.value===l?null:l);var l}},f(a.name),11,D))),128))])])]),i("section",G,[(b(!0),e(p,null,m(na.value,(a,s)=>(b(),e("div",{key:a.id,class:"tool-card",style:y({"--index":s}),onClick:s=>(async a=>{if(console.log("点击工具:",a.name,"URL:",a.url),!a.url||""===a.url.trim())return console.warn("工具URL为空:",a),void alert("该工具暂无可用链接");try{let s=a.url.trim();s.startsWith("http://")||s.startsWith("https://")||(s="https://"+s),await j.incrementClickCount(a.id),window.open(s,"_blank","noopener,noreferrer")}catch(s){console.error("打开链接失败:",s),alert("无法打开该链接，请检查URL是否正确")}})(a)},[i("button",{class:g(["favorite-btn",{favorited:a.isFavorite}]),onClick:_(s=>(async a=>{la.isAuthenticated?await j.toggleFavorite(a.id):ta.push("/auth/login")})(a),["stop"])},[o(r(C),{class:"icon"})],10,K),i("div",M,[i("div",N,f(a.icon||"🔧"),1),i("h3",O,f(a.name),1),i("p",Q,f(a.short_description||a.description.slice(0,50)+"..."),1)]),i("div",T,[i("p",X,f(a.description),1),i("div",Y,[(b(!0),e(p,null,m((a.tags||[]).slice(0,3),a=>(b(),e("span",{key:a,class:"tool-tag"},f(a),1))),128)),(a.tags||[]).length>3?(b(),e("span",Z," +"+f((a.tags||[]).length-3),1)):k("",!0)])])],12,J))),128)),0===na.value.length?(b(),e("div",aa,[s[7]||(s[7]=i("div",{class:"empty-icon"},"🔍",-1)),s[8]||(s[8]=i("h3",null,"未找到相关工具",-1)),s[9]||(s[9]=i("p",null,"尝试使用其他关键词搜索，或浏览其他分类",-1)),i("button",{class:"empty-action",onClick:da}," 清除搜索条件 ")])):k("",!0)])])])]),s[10]||(s[10]=i("footer",{class:"footer"},[i("div",{class:"container"},[i("p",null,"© 2024 高效工具导航站 | 让您的工作更智能、更高效")])],-1))])}}}),[["__scopeId","data-v-c277f1a9"]]);export{sa as default};
