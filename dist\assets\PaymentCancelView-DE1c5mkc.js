import{d as s,c as a,_ as c}from"./index-DfMIV_22.js";import{d as t,r as l,o as e,m as n,q as o,t as i,z as p,u,N as d,E as r,b0 as v,K as m,$ as _,H as f,_ as b,bc as h,aa as y,ab as x,B as k}from"./vendor-CnBwbdGX.js";const w={class:"payment-cancel-view"},C={class:"cancel-container"},g={class:"cancel-content"},j={class:"cancel-icon"},q={key:0,class:"order-info"},z={class:"info-item"},N={class:"value"},$={class:"info-item"},B={class:"value"},D={class:"next-steps"},E={class:"steps-list"},H={class:"step-item"},I={class:"step-item"},K={class:"step-content"},L={class:"step-item"},P={class:"action-buttons"},S={class:"support-info"},V={class:"contact-methods"},A={href:"mailto:<EMAIL>",class:"contact-item"},F={href:"tel:************",class:"contact-item"},G=c(t({__name:"PaymentCancelView",setup(c){const t=s(),G=a(),J=l(""),M=l(""),O=()=>{J.value?G.push(`/payment?order=${J.value}`):G.push("/payment")};return e(()=>{(()=>{const s=t.query.order;s&&(J.value=s),M.value=(new Date).toLocaleString("zh-CN")})()}),(s,a)=>{const c=b("router-link");return k(),n("div",w,[o("div",C,[o("div",g,[o("div",j,[p(u(d),{class:"icon"})]),a[14]||(a[14]=o("h1",{class:"cancel-title"},"支付已取消",-1)),a[15]||(a[15]=o("p",{class:"cancel-message"},"您的支付已被取消，订单未完成",-1)),J.value?(k(),n("div",q,[o("div",z,[a[0]||(a[0]=o("span",{class:"label"},"订单号:",-1)),o("span",N,r(J.value),1)]),o("div",$,[a[1]||(a[1]=o("span",{class:"label"},"取消时间:",-1)),o("span",B,r(M.value),1)])])):i("",!0),o("div",D,[a[8]||(a[8]=o("h3",null,"接下来您可以：",-1)),o("div",E,[o("div",H,[p(u(v),{class:"step-icon"}),o("div",{class:"step-content"},[a[2]||(a[2]=o("h4",null,"重新支付",-1)),a[3]||(a[3]=o("p",null,"返回支付页面完成订单支付",-1)),o("button",{class:"step-action",onClick:O}," 重新支付 ")])]),o("div",I,[p(u(m),{class:"step-icon"}),o("div",K,[a[5]||(a[5]=o("h4",null,"继续购物",-1)),a[6]||(a[6]=o("p",null,"浏览更多优质产品",-1)),p(c,{to:"/products",class:"step-action"},{default:_(()=>a[4]||(a[4]=[f(" 继续购物 ")])),_:1,__:[4]})])]),o("div",L,[p(u(h),{class:"step-icon"}),a[7]||(a[7]=o("div",{class:"step-content"},[o("h4",null,"联系客服"),o("p",null,"如有疑问，请联系我们的客服团队")],-1))])])]),o("div",P,[p(c,{to:"/",class:"btn btn-secondary"},{default:_(()=>a[9]||(a[9]=[f(" 返回首页 ")])),_:1,__:[9]}),p(c,{to:"/products",class:"btn btn-primary"},{default:_(()=>a[10]||(a[10]=[f(" 继续购物 ")])),_:1,__:[10]})]),o("div",S,[a[13]||(a[13]=o("p",null,"如需帮助，请联系我们的客服团队",-1)),o("div",V,[o("a",A,[p(u(y),{class:"contact-icon"}),a[11]||(a[11]=f(" <EMAIL> "))]),o("a",F,[p(u(x),{class:"contact-icon"}),a[12]||(a[12]=f(" ************ "))])])])])])])}}}),[["__scopeId","data-v-2c046c66"]]);export{G as default};
