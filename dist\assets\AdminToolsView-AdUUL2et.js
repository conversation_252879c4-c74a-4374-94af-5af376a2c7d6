import{d as e,r as a,c as t,o,m as s,q as r,t as l,z as i,H as c,u as n,aK as u,b0 as d,R as v,U as m,C as p,E as g,ae as y,F as f,A as h,a6 as w,b1 as _,b2 as b,v as k,aQ as T,aW as E,X as C,y as O,T as q,B as S}from"./vendor-CnBwbdGX.js";import{s as I,T as F,h as L,u as U,a as V,_ as R}from"./index-DfMIV_22.js";function $(e){var a;return(null==(a=e.category)?void 0:a.id)?e.category.id:e.categoryId?e.categoryId:e.category_id?e.category_id:null}const x="active";class z{static async getTools(e){try{let a=I.from(F.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status",x);(null==e?void 0:e.query)&&(a=a.or(`name.ilike.%${e.query}%,description.ilike.%${e.query}%`)),(null==e?void 0:e.category)&&"all"!==e.category&&(a=a.eq("category_id",e.category));const t=(null==e?void 0:e.sortBy)||"sort_order",o=(null==e?void 0:e.sortOrder)||"asc";a=a.order(t,{ascending:"asc"===o});const s=(null==e?void 0:e.page)||1,r=(null==e?void 0:e.limit)||20,l=(s-1)*r;a=a.range(l,l+r-1);const{data:i,error:c,count:n}=await a;if(c)throw new Error(L(c));return{items:(i||[]).map(this.transformToolRow),total:n||0,page:s,limit:r,hasMore:(n||0)>l+r}}catch(a){throw console.error("Error fetching tools:",a),a}}static async getTool(e){try{const{data:a,error:t}=await I.from(F.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("id",e).single();if(t)throw new Error(L(t));return this.transformToolRow(a)}catch(a){throw console.error("Error fetching tool:",a),a}}static async createTool(e){try{!function(e,a,t="Entity"){const o=[];for(const s of a)e[s]||o.push(s);if(o.length>0)throw new Error(`${t} validation failed: missing required fields: ${o.join(", ")}`)}(e,["name","description","url"],"Tool");const a=function(e,a="Category"){const t=$(e);if(!t)throw new Error(`${a} is required`);return t}(e),{data:t,error:o}=await I.from(F.TOOLS).insert({name:e.name,description:e.description,url:e.url,category_id:a,icon:e.icon,is_featured:e.is_featured||!1,status:x,meta_title:e.meta_title,meta_description:e.meta_description,sort_order:e.sort_order||0}).select("\n          *,\n          category:categories(*)\n        ").single();if(o)throw new Error(L(o));return this.transformToolRow(t)}catch(a){throw console.error("Error creating tool:",a),a}}static async updateTool(e,a){try{const t={};a.name&&(t.name=a.name),a.description&&(t.description=a.description),a.url&&(t.url=a.url);const o=$(a);o&&(t.category_id=o),void 0!==a.icon&&(t.icon=a.icon),void 0!==a.is_featured&&(t.is_featured=a.is_featured),a.status&&(t.status=a.status),void 0!==a.meta_title&&(t.meta_title=a.meta_title),void 0!==a.meta_description&&(t.meta_description=a.meta_description),void 0!==a.sort_order&&(t.sort_order=a.sort_order),t.updated_at=(new Date).toISOString();const{data:s,error:r}=await I.from(F.TOOLS).update(t).eq("id",e).select("\n          *,\n          category:categories(*)\n        ").single();if(r)throw new Error(L(r));return this.transformToolRow(s)}catch(t){throw console.error("Error updating tool:",t),t}}static async deleteTool(e){try{const{error:a}=await I.from(F.TOOLS).delete().eq("id",e);if(a)throw new Error(L(a))}catch(a){throw console.error("Error deleting tool:",a),a}}static async incrementClickCount(e){try{const{error:a}=await I.rpc("increment_click_count",{tool_id:e});if(a)throw new Error(L(a))}catch(a){throw console.error("Error incrementing click count:",a),a}}static async updateFavoriteStatus(e,a){try{const{error:t}=await I.from(F.TOOLS).update({is_favorite:a,updated_at:(new Date).toISOString()}).eq("id",e);if(t)throw new Error(L(t))}catch(t){throw console.error("Error updating favorite status:",t),t}}static async getPopularTools(e=10){try{const{data:a,error:t}=await I.from(F.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status",x).order("click_count",{ascending:!1}).limit(e);if(t)throw new Error(L(t));return(a||[]).map(this.transformToolRow)}catch(a){throw console.error("Error fetching popular tools:",a),a}}static async getFeaturedTools(e=6){try{const{data:a,error:t}=await I.from(F.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status",x).eq("is_featured",!0).order("sort_order",{ascending:!0}).limit(e);if(t)throw new Error(L(t));return(a||[]).map(this.transformToolRow)}catch(a){throw console.error("Error fetching featured tools:",a),a}}static async searchTools(e,a=20){try{const{data:t,error:o}=await I.from(F.TOOLS).select("\n          *,\n          category:categories(*)\n        ").eq("status",x).or(`name.ilike.%${e}%,description.ilike.%${e}%`).order("click_count",{ascending:!1}).limit(a);if(o)throw new Error(L(o));return(t||[]).map(this.transformToolRow)}catch(t){throw console.error("Error searching tools:",t),t}}static transformToolRow(e){return{id:e.id,name:e.name,description:e.description,url:e.url,icon:e.icon,category_id:e.category_id,tags:[],is_favorite:e.is_favorite,click_count:e.click_count,is_featured:e.is_featured,status:e.status,created_at:e.created_at,updated_at:e.updated_at,created_by:e.created_by,meta_title:e.meta_title,meta_description:e.meta_description,sort_order:e.sort_order}}}const j={class:"admin-tools-view"},A={class:"admin-actions"},B={class:"search-tools"},D={class:"tools-summary"},M={class:"stats-grid"},H={class:"stat-card"},K={class:"stat-number"},P={class:"stat-card"},Q={class:"stat-number"},W={class:"stat-card"},G={class:"stat-number"},J={class:"stat-card"},N={class:"stat-number"},X={class:"tools-management"},Y={class:"tools-header"},Z={class:"filter-controls"},ee=["value"],ae={class:"tools-table"},te={class:"col-icon"},oe={class:"tool-icon"},se={class:"col-name"},re={class:"tool-name"},le={class:"tool-description"},ie={class:"col-category"},ce={class:"category-badge"},ne={class:"col-status"},ue={class:"col-clicks"},de={class:"col-actions"},ve=["onClick"],me=["onClick"],pe=["onClick","title"],ge={key:0,class:"pagination"},ye=["disabled"],fe={class:"page-info"},he=["disabled"],we={class:"modal-header"},_e={class:"form-grid"},be={class:"form-group"},ke={class:"form-group"},Te={class:"form-group full-width"},Ee={class:"form-group"},Ce={class:"form-group"},Oe=["value"],qe={class:"form-group"},Se={class:"form-group"},Ie={class:"form-actions"},Fe=["disabled"],Le=R(e({__name:"AdminToolsView",setup(e){const I=U(),F=V(),L=a(!1),R=a(!1),$=a(""),x=a("success"),Le=a(""),Ue=a(""),Ve=a(""),Re=a(1),$e=a(10),xe=a(!1),ze=a(!1),je=a(null),Ae=a({name:"",description:"",url:"",icon:"🔧",categoryId:"",status:"active",isFeatured:!1}),Be=t(()=>{let e=I.tools;if(Le.value){const a=Le.value.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(a)||e.description.toLowerCase().includes(a))}return Ue.value&&(e=e.filter(e=>e.categoryId===Ue.value)),Ve.value&&(e=e.filter(e=>e.status===Ve.value)),e}),De=t(()=>I.tools.filter(e=>e.isFeatured).length),Me=t(()=>I.tools.filter(e=>"active"===e.status).length),He=t(()=>Math.ceil(Be.value.length/$e.value)),Ke=t(()=>{const e=(Re.value-1)*$e.value,a=e+$e.value;return Be.value.slice(e,a)}),Pe=async()=>{try{L.value=!0,await I.initialize(),await F.initialize(),$.value="工具列表已刷新",x.value="success"}catch(e){$.value="刷新失败",x.value="error"}finally{L.value=!1}},Qe=()=>{xe.value=!1,ze.value=!1,je.value=null,Ae.value={name:"",description:"",url:"",icon:"🔧",categoryId:"",status:"active",isFeatured:!1}},We=async()=>{try{R.value=!0,$.value="",ze.value&&je.value?(await z.updateTool(je.value.id,{name:Ae.value.name,description:Ae.value.description,url:Ae.value.url,icon:Ae.value.icon,category_id:Ae.value.categoryId,status:Ae.value.status,is_featured:Ae.value.isFeatured}),$.value="工具更新成功！"):(await z.createTool({name:Ae.value.name,description:Ae.value.description,url:Ae.value.url,icon:Ae.value.icon,category_id:Ae.value.categoryId,status:Ae.value.status,is_featured:Ae.value.isFeatured,click_count:0,sort_order:0}),$.value="工具创建成功！"),x.value="success",Qe(),await Pe()}catch(e){console.error("保存工具失败:",e),$.value="保存工具失败: "+(e instanceof Error?e.message:"未知错误"),x.value="error"}finally{R.value=!1}};return o(async()=>{await I.initialize(),await F.initialize()}),(e,a)=>(S(),s("div",j,[a[33]||(a[33]=r("div",{class:"admin-header"},[r("h1",null,"工具管理"),r("p",null,"添加、编辑和管理工具数据")],-1)),r("div",A,[r("button",{class:"action-button primary",onClick:a[0]||(a[0]=e=>xe.value=!0)},[i(n(u),{class:"icon"}),a[14]||(a[14]=c(" 添加新工具 "))]),r("button",{class:"action-button secondary",onClick:Pe},[i(n(d),{class:"icon"}),a[15]||(a[15]=c(" 刷新列表 "))]),r("div",B,[v(r("input",{"onUpdate:modelValue":a[1]||(a[1]=e=>Le.value=e),type:"text",placeholder:"搜索工具...",class:"search-input"},null,512),[[m,Le.value]])])]),$.value?(S(),s("div",{key:0,class:p(["message",x.value])},g($.value),3)):l("",!0),r("div",D,[r("div",M,[r("div",H,[r("div",K,g(Be.value.length),1),a[16]||(a[16]=r("div",{class:"stat-label"},"总工具数",-1))]),r("div",P,[r("div",Q,g(De.value),1),a[17]||(a[17]=r("div",{class:"stat-label"},"推荐工具",-1))]),r("div",W,[r("div",G,g(n(F).categories.length),1),a[18]||(a[18]=r("div",{class:"stat-label"},"分类数",-1))]),r("div",J,[r("div",N,g(Me.value),1),a[19]||(a[19]=r("div",{class:"stat-label"},"活跃工具",-1))])])]),r("div",X,[r("div",Y,[a[22]||(a[22]=r("h2",null,"工具列表",-1)),r("div",Z,[v(r("select",{"onUpdate:modelValue":a[2]||(a[2]=e=>Ue.value=e),class:"category-filter"},[a[20]||(a[20]=r("option",{value:""},"全部分类",-1)),(S(!0),s(f,null,h(n(F).categories,e=>(S(),s("option",{key:e.id,value:e.id},g(e.name),9,ee))),128))],512),[[y,Ue.value]]),v(r("select",{"onUpdate:modelValue":a[3]||(a[3]=e=>Ve.value=e),class:"status-filter"},a[21]||(a[21]=[r("option",{value:""},"全部状态",-1),r("option",{value:"active"},"活跃",-1),r("option",{value:"inactive"},"停用",-1)]),512),[[y,Ve.value]])])]),r("div",ae,[a[23]||(a[23]=w('<div class="table-header" data-v-ea64fe49><div class="col-icon" data-v-ea64fe49>图标</div><div class="col-name" data-v-ea64fe49>名称</div><div class="col-category" data-v-ea64fe49>分类</div><div class="col-status" data-v-ea64fe49>状态</div><div class="col-clicks" data-v-ea64fe49>点击数</div><div class="col-actions" data-v-ea64fe49>操作</div></div>',1)),(S(!0),s(f,null,h(Ke.value,e=>{var a;return S(),s("div",{key:e.id,class:"table-row"},[r("div",te,[r("span",oe,g(e.icon),1)]),r("div",se,[r("div",re,g(e.name),1),r("div",le,g(e.description),1)]),r("div",ie,[r("span",ce,g((null==(a=e.category)?void 0:a.name)||"未分类"),1)]),r("div",ne,[r("span",{class:p(["status-badge","active"===e.status?"active":"inactive"])},g("active"===e.status?"活跃":"停用"),3)]),r("div",ue,g(e.clickCount||0),1),r("div",de,[r("button",{class:"action-btn edit",onClick:a=>(e=>{je.value=e,Ae.value={name:e.name,description:e.description,url:e.url,icon:e.icon,categoryId:e.categoryId,status:e.status,isFeatured:e.isFeatured},ze.value=!0})(e),title:"编辑"},[i(n(_),{class:"icon"})],8,ve),r("button",{class:"action-btn delete",onClick:a=>(async e=>{if(confirm(`确定要删除工具 "${e.name}" 吗？此操作不可恢复。`))try{await z.deleteTool(e.id),$.value="工具删除成功！",x.value="success",await Pe()}catch(a){console.error("删除工具失败:",a),$.value="删除工具失败: "+(a instanceof Error?a.message:"未知错误"),x.value="error"}})(e),title:"删除"},[i(n(b),{class:"icon"})],8,me),r("button",{class:"action-btn toggle",onClick:a=>(async e=>{try{const a="active"===e.status?"inactive":"active";await z.updateTool(e.id,{status:a}),$.value=`工具已${"active"===a?"启用":"停用"}！`,x.value="success",await Pe()}catch(a){console.error("切换工具状态失败:",a),$.value="操作失败",x.value="error"}})(e),title:"active"===e.status?"停用":"启用"},["active"===e.status?(S(),k(n(T),{key:0,class:"icon"})):(S(),k(n(E),{key:1,class:"icon"}))],8,pe)])])}),128))]),He.value>1?(S(),s("div",ge,[r("button",{disabled:1===Re.value,onClick:a[4]||(a[4]=e=>Re.value--),class:"page-btn"}," 上一页 ",8,ye),r("span",fe," 第 "+g(Re.value)+" 页，共 "+g(He.value)+" 页 ",1),r("button",{disabled:Re.value===He.value,onClick:a[5]||(a[5]=e=>Re.value++),class:"page-btn"}," 下一页 ",8,he)])):l("",!0)]),xe.value||ze.value?(S(),s("div",{key:1,class:"modal-overlay",onClick:Qe},[r("div",{class:"modal-content",onClick:a[13]||(a[13]=O(()=>{},["stop"]))},[r("div",we,[r("h3",null,g(xe.value?"添加新工具":"编辑工具"),1),r("button",{class:"close-btn",onClick:Qe},[i(n(C),{class:"icon"})])]),r("form",{onSubmit:O(We,["prevent"]),class:"tool-form"},[r("div",_e,[r("div",be,[a[24]||(a[24]=r("label",null,"工具名称 *",-1)),v(r("input",{"onUpdate:modelValue":a[6]||(a[6]=e=>Ae.value.name=e),type:"text",required:"",placeholder:"输入工具名称"},null,512),[[m,Ae.value.name]])]),r("div",ke,[a[25]||(a[25]=r("label",null,"工具图标",-1)),v(r("input",{"onUpdate:modelValue":a[7]||(a[7]=e=>Ae.value.icon=e),type:"text",placeholder:"🔧"},null,512),[[m,Ae.value.icon]])]),r("div",Te,[a[26]||(a[26]=r("label",null,"工具描述 *",-1)),v(r("textarea",{"onUpdate:modelValue":a[8]||(a[8]=e=>Ae.value.description=e),required:"",placeholder:"描述工具的功能和用途",rows:"3"},null,512),[[m,Ae.value.description]])]),r("div",Ee,[a[27]||(a[27]=r("label",null,"工具链接 *",-1)),v(r("input",{"onUpdate:modelValue":a[9]||(a[9]=e=>Ae.value.url=e),type:"url",required:"",placeholder:"https://example.com"},null,512),[[m,Ae.value.url]])]),r("div",Ce,[a[29]||(a[29]=r("label",null,"分类 *",-1)),v(r("select",{"onUpdate:modelValue":a[10]||(a[10]=e=>Ae.value.categoryId=e),required:""},[a[28]||(a[28]=r("option",{value:""},"选择分类",-1)),(S(!0),s(f,null,h(n(F).categories,e=>(S(),s("option",{key:e.id,value:e.id},g(e.name),9,Oe))),128))],512),[[y,Ae.value.categoryId]])]),r("div",qe,[a[31]||(a[31]=r("label",null,"状态",-1)),v(r("select",{"onUpdate:modelValue":a[11]||(a[11]=e=>Ae.value.status=e)},a[30]||(a[30]=[r("option",{value:"active"},"活跃",-1),r("option",{value:"inactive"},"停用",-1)]),512),[[y,Ae.value.status]])]),r("div",Se,[r("label",null,[v(r("input",{"onUpdate:modelValue":a[12]||(a[12]=e=>Ae.value.isFeatured=e),type:"checkbox"},null,512),[[q,Ae.value.isFeatured]]),a[32]||(a[32]=c(" 推荐工具 "))])])]),r("div",Ie,[r("button",{type:"button",onClick:Qe,class:"cancel-btn"}," 取消 "),r("button",{type:"submit",disabled:R.value,class:"save-btn"},g(R.value?"保存中...":"保存"),9,Fe)])],32)])])):l("",!0)]))}}),[["__scopeId","data-v-ea64fe49"]]);export{Le as default};
