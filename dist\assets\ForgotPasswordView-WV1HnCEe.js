import{d as a,r as e,W as l,m as s,q as t,y as u,t as n,R as o,U as r,E as i,H as v,z as c,$ as d,_ as m,B as f}from"./vendor-CnBwbdGX.js";import{_ as p}from"./index-DfMIV_22.js";const b={class:"forgot-password-view"},g={class:"form-group"},y=["disabled"],_=["disabled"],h={key:0,class:"loading-spinner"},k={key:0,class:"error-message"},w={key:1,class:"success-state"},I={class:"success-actions"},x=["disabled"],E={class:"forgot-footer"},P=p(a({__name:"ForgotPasswordView",setup(a){const p=e(!1),P=e(null),j=e(""),q=e(!1),T=e(0);let U=null;const V=async()=>{try{p.value=!0,P.value=null,await new Promise(a=>setTimeout(a,1e3)),q.value=!0,z()}catch(a){P.value=a instanceof Error?a.message:"发送失败，请重试"}finally{p.value=!1}},$=async()=>{if(!(T.value>0))try{p.value=!0,P.value=null,await new Promise(a=>setTimeout(a,500)),z()}catch(a){P.value=a instanceof Error?a.message:"重新发送失败，请重试"}finally{p.value=!1}},z=()=>{T.value=60,U=setInterval(()=>{T.value--,T.value<=0&&(clearInterval(U),U=null)},1e3)};return l(()=>{U&&clearInterval(U)}),(a,e)=>{const l=m("router-link");return f(),s("div",b,[e[10]||(e[10]=t("div",{class:"forgot-header"},[t("h1",null,"忘记密码"),t("p",null,"输入您的邮箱地址，我们将发送重置密码的链接")],-1)),q.value?(f(),s("div",w,[e[5]||(e[5]=t("div",{class:"success-icon"},"✅",-1)),e[6]||(e[6]=t("h3",null,"邮件已发送",-1)),t("p",null,[e[2]||(e[2]=v(" 我们已向 ")),t("strong",null,i(j.value),1),e[3]||(e[3]=v(" 发送了重置密码的链接 "))]),e[7]||(e[7]=t("p",{class:"help-text"}," 请检查您的邮箱（包括垃圾邮件文件夹），并点击链接重置密码 ",-1)),t("div",I,[t("button",{class:"resend-btn",disabled:T.value>0,onClick:$},i(T.value>0?`${T.value}秒后可重发`:"重新发送"),9,x),c(l,{to:"/auth/login",class:"back-btn"},{default:d(()=>e[4]||(e[4]=[v("返回登录")])),_:1,__:[4]})])])):(f(),s("form",{key:0,class:"forgot-form",onSubmit:u(V,["prevent"])},[t("div",g,[e[1]||(e[1]=t("label",{for:"email"},"邮箱地址",-1)),o(t("input",{id:"email","onUpdate:modelValue":e[0]||(e[0]=a=>j.value=a),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:p.value},null,8,y),[[r,j.value]])]),t("button",{type:"submit",class:"submit-btn",disabled:p.value||!j.value},[p.value?(f(),s("div",h)):n("",!0),t("span",null,i(p.value?"发送中...":"发送重置链接"),1)],8,_),P.value?(f(),s("div",k,i(P.value),1)):n("",!0)],32)),t("div",E,[t("p",null,[e[9]||(e[9]=v(" 记起密码了？ ")),c(l,{to:"/auth/login",class:"login-link"},{default:d(()=>e[8]||(e[8]=[v("立即登录")])),_:1,__:[8]})])])])}}}),[["__scopeId","data-v-5fa570f1"]]);export{P as default};
