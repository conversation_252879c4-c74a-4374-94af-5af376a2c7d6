import{d as l,r as s,c as a,o as e,w as u,m as t,q as n,t as i,H as c,E as o,z as r,R as v,ae as d,F as p,A as h,B as y}from"./vendor-CnBwbdGX.js";import{d as g,c as m,_ as k}from"./index-DfMIV_22.js";import{E as q,s as f}from"./EnhancedSearchBox-CEYnlaFY.js";const C={class:"search-results-view"},w={class:"container"},_={class:"search-header"},b={class:"search-info"},x={key:0},O={key:0},P={class:"search-box"},S={key:0,class:"loading-state"},j={key:1,class:"search-results"},E={class:"search-stats"},R={class:"search-filters"},B={key:0,class:"search-suggestions"},U={class:"suggestions-list"},V=["onClick"],$={class:"results-content"},z={key:0,class:"results-section"},A={class:"tools-grid"},F=["onClick"],H={class:"tool-icon"},I={class:"tool-info"},T={class:"tool-meta"},D={class:"category"},G={class:"clicks"},J={key:1,class:"results-section"},K={class:"products-grid"},L=["onClick"],M={class:"product-image"},N=["src","alt"],Q={class:"product-info"},W={class:"product-meta"},X={class:"price"},Y={class:"rating"},Z={key:2,class:"no-results"},ll={key:2,class:"error-state"},sl=k(l({__name:"SearchResultsView",setup(l){const k=g(),sl=m(),al=s(!1),el=s(null),ul=s(null),tl=s(""),nl=s("all"),il=a(()=>ul.value?ul.value.items.filter(l=>"tool"===l.type||!l.type||l.hasOwnProperty("clickCount")||l.hasOwnProperty("category_id")):[]),cl=a(()=>ul.value?ul.value.items.filter(l=>"product"===l.type||l.hasOwnProperty("price")||l.hasOwnProperty("rating")):[]),ol=async()=>{if(tl.value.trim())try{al.value=!0,el.value=null;const l=await f.search({query:tl.value,type:nl.value,limit:50});ul.value=l}catch(l){el.value="搜索失败，请稍后重试",console.error("搜索错误:",l)}finally{al.value=!1}},rl=l=>{ul.value=l,tl.value=l.query,sl.push({name:"SearchResults",query:{q:l.query,type:nl.value}})},vl=()=>{ol()};return e(()=>{tl.value=k.query.q||"",nl.value=k.query.type||"all",tl.value&&ol()}),u(()=>k.query,l=>{tl.value=l.q||"",nl.value=l.type||"all",tl.value&&ol()}),(l,s)=>(y(),t("div",C,[n("div",w,[n("div",_,[n("div",b,[s[3]||(s[3]=n("h1",null,"搜索结果",-1)),tl.value?(y(),t("p",x,[s[1]||(s[1]=c(' 关于 "')),n("strong",null,o(tl.value),1),s[2]||(s[2]=c('" 的搜索结果 ')),ul.value?(y(),t("span",O,"("+o(ul.value.total)+" 个结果)",1)):i("",!0)])):i("",!0)]),n("div",P,[r(q,{"initial-query":tl.value,placeholder:"重新搜索...",onSearch:rl},null,8,["initial-query"])])]),al.value?(y(),t("div",S,s[4]||(s[4]=[n("div",{class:"loading-spinner"},null,-1),n("p",null,"正在搜索...",-1)]))):ul.value?(y(),t("div",j,[n("div",E,[n("p",null," 找到 "+o(ul.value.total)+" 个结果，用时 "+o(ul.value.searchTime)+"ms ",1),n("div",R,[v(n("select",{"onUpdate:modelValue":s[0]||(s[0]=l=>nl.value=l),onChange:vl},s[5]||(s[5]=[n("option",{value:"all"},"全部类型",-1),n("option",{value:"tools"},"工具",-1),n("option",{value:"products"},"产品",-1),n("option",{value:"categories"},"分类",-1)]),544),[[d,nl.value]])])]),ul.value.suggestions.length>0?(y(),t("div",B,[s[6]||(s[6]=n("h3",null,"相关建议",-1)),n("div",U,[(y(!0),t(p,null,h(ul.value.suggestions,l=>(y(),t("button",{key:l,class:"suggestion-tag",onClick:s=>(l=>{tl.value=l,ol()})(l)},o(l),9,V))),128))])])):i("",!0),n("div",$,[il.value.length>0?(y(),t("div",z,[n("h2",null,"工具 ("+o(il.value.length)+")",1),n("div",A,[(y(!0),t(p,null,h(il.value,l=>{var s;return y(),t("div",{key:l.id,class:"tool-card",onClick:s=>(l=>{l.url?window.open(l.url,"_blank"):sl.push(`/tools/${l.id}`)})(l)},[n("div",H,o(l.icon),1),n("div",I,[n("h3",null,o(l.name),1),n("p",null,o(l.description),1),n("div",T,[n("span",D,o(null==(s=l.category)?void 0:s.name),1),n("span",G,o(l.clickCount)+" 次使用",1)])])],8,F)}),128))])])):i("",!0),cl.value.length>0?(y(),t("div",J,[n("h2",null,"产品 ("+o(cl.value.length)+")",1),n("div",K,[(y(!0),t(p,null,h(cl.value,l=>(y(),t("div",{key:l.id,class:"product-card",onClick:s=>(l=>{sl.push(`/product/${l.id}`)})(l)},[n("div",M,[n("img",{src:l.imageUrl,alt:l.name},null,8,N)]),n("div",Q,[n("h3",null,o(l.name),1),n("p",null,o(l.description),1),n("div",W,[n("span",X,"¥"+o(l.price),1),n("span",Y,"★ "+o(l.rating),1)])])],8,L))),128))])])):i("",!0),0===ul.value.total?(y(),t("div",Z,s[7]||(s[7]=[n("div",{class:"no-results-icon"},"🔍",-1),n("h3",null,"未找到相关结果",-1),n("p",null,"尝试使用不同的关键词或检查拼写",-1),n("div",{class:"search-tips"},[n("h4",null,"搜索建议："),n("ul",null,[n("li",null,"使用更通用的关键词"),n("li",null,"检查拼写是否正确"),n("li",null,"尝试使用同义词"),n("li",null,"减少搜索词的数量")])],-1)]))):i("",!0)])])):el.value?(y(),t("div",ll,[s[8]||(s[8]=n("div",{class:"error-icon"},"⚠️",-1)),s[9]||(s[9]=n("h3",null,"搜索出错",-1)),n("p",null,o(el.value),1),n("button",{class:"retry-button",onClick:ol},"重试")])):i("",!0)])]))}}),[["__scopeId","data-v-70511175"]]);export{sl as default};
