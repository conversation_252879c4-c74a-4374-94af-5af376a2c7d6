import{d as e,b as l,r as a,m as s,a6 as t,q as r,y as o,H as i,F as d,A as n,C as u,v as c,D as m,E as b,aF as g,aG as p,at as x,aw as y,aH as v,R as f,U as h,V as w,t as k,z as j,u as C,a5 as V,X as U,aI as q,J as F,B as I}from"./vendor-CnBwbdGX.js";const z={class:"min-h-screen bg-gray-50"},A={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},B={class:"bg-white rounded-lg shadow-md p-8"},D={class:"grid grid-cols-2 md:grid-cols-5 gap-3"},H=["onClick"],P={class:"text-xs font-medium"},_={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},E={class:"mt-1 text-sm text-gray-500"},G={class:"flex space-x-4"},J=["onUpdate:modelValue","value"],M={class:"text-sm text-gray-700"},O={class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},R={key:0,class:"mt-3 space-y-2"},S={class:"text-sm text-gray-700"},T=["onClick"],W={class:"flex justify-end space-x-4"},$=["disabled"],K={class:"mt-12 bg-blue-50 rounded-lg p-6"},L={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},N=e({__name:"FeedbackView",setup(e){const N=[{value:"bug",label:"错误报告",icon:g},{value:"feature",label:"功能建议",icon:p},{value:"improvement",label:"改进建议",icon:x},{value:"question",label:"使用问题",icon:y},{value:"other",label:"其他",icon:v}],Q=[{value:1,label:"低"},{value:2,label:"中"},{value:3,label:"高"},{value:4,label:"紧急"}],X=l({category:"other",name:"",email:"",subject:"",message:"",priority:2}),Y=a(!1),Z=a([]),ee=e=>{const l=e.target;if(l.files){const e=Array.from(l.files);Z.value.push(...e)}},le=()=>{Object.assign(X,{category:"other",name:"",email:"",subject:"",message:"",priority:2}),Z.value=[]},ae=async()=>{if(X.email&&X.subject&&X.message){Y.value=!0;try{console.log("提交反馈:",{...X,attachments:Z.value.map(e=>e.name)}),await new Promise(e=>setTimeout(e,2e3)),alert("反馈提交成功！我们会尽快回复您。"),le()}catch(e){console.error("提交失败:",e),alert("提交失败，请稍后重试")}finally{Y.value=!1}}else alert("请填写必填字段")};return document.title="意见反馈 - 工具导航站",(e,l)=>(I(),s("div",z,[l[16]||(l[16]=t('<div class="bg-white shadow-sm"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="text-center"><h1 class="text-3xl font-bold text-gray-900">意见反馈</h1><p class="mt-4 text-lg text-gray-600"> 您的意见对我们很重要，帮助我们不断改进服务 </p></div></div></div>',1)),r("div",A,[r("div",B,[r("form",{onSubmit:o(ae,["prevent"]),class:"space-y-6"},[r("div",null,[l[5]||(l[5]=r("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[i(" 反馈类型 "),r("span",{class:"text-red-500"},"*")],-1)),r("div",D,[(I(),s(d,null,n(N,e=>r("button",{key:e.value,type:"button",onClick:l=>X.category=e.value,class:u(["p-3 border rounded-lg text-center transition-colors",X.category===e.value?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:border-gray-400"])},[(I(),c(m(e.icon),{class:"w-5 h-5 mx-auto mb-1"})),r("div",P,b(e.label),1)],10,H)),64))])]),r("div",_,[r("div",null,[l[6]||(l[6]=r("label",{for:"name",class:"block text-sm font-medium text-gray-700 mb-2"}," 姓名 ",-1)),f(r("input",{id:"name","onUpdate:modelValue":l[0]||(l[0]=e=>X.name=e),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的姓名"},null,512),[[h,X.name]])]),r("div",null,[l[7]||(l[7]=r("label",{for:"email",class:"block text-sm font-medium text-gray-700 mb-2"},[i(" 邮箱 "),r("span",{class:"text-red-500"},"*")],-1)),f(r("input",{id:"email","onUpdate:modelValue":l[1]||(l[1]=e=>X.email=e),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的邮箱地址"},null,512),[[h,X.email]])])]),r("div",null,[l[8]||(l[8]=r("label",{for:"subject",class:"block text-sm font-medium text-gray-700 mb-2"},[i(" 主题 "),r("span",{class:"text-red-500"},"*")],-1)),f(r("input",{id:"subject","onUpdate:modelValue":l[2]||(l[2]=e=>X.subject=e),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请简要描述您的问题或建议"},null,512),[[h,X.subject]])]),r("div",null,[l[9]||(l[9]=r("label",{for:"message",class:"block text-sm font-medium text-gray-700 mb-2"},[i(" 详细描述 "),r("span",{class:"text-red-500"},"*")],-1)),f(r("textarea",{id:"message","onUpdate:modelValue":l[3]||(l[3]=e=>X.message=e),rows:"6",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"请详细描述您遇到的问题、建议或意见..."},null,512),[[h,X.message]]),r("div",E,b(X.message.length)+"/1000 字符 ",1)]),r("div",null,[l[10]||(l[10]=r("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 优先级 ",-1)),r("div",G,[(I(),s(d,null,n(Q,e=>r("label",{key:e.value,class:"flex items-center"},[f(r("input",{"onUpdate:modelValue":e=>X.priority=e,value:e.value,type:"radio",class:"mr-2 text-blue-600 focus:ring-blue-500"},null,8,J),[[w,X.priority]]),r("span",M,b(e.label),1)])),64))])]),r("div",null,[l[13]||(l[13]=r("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 附件（可选） ",-1)),r("div",O,[j(C(V),{class:"w-8 h-8 text-gray-400 mx-auto mb-2"}),l[11]||(l[11]=r("p",{class:"text-sm text-gray-600 mb-2"}," 拖拽文件到此处或点击上传 ",-1)),r("input",{type:"file",multiple:"",accept:"image/*,.pdf,.doc,.docx,.txt",class:"hidden",ref:"fileInput",onChange:ee},null,544),r("button",{type:"button",onClick:l[4]||(l[4]=l=>e.$refs.fileInput.click()),class:"text-blue-600 hover:text-blue-800 text-sm font-medium"}," 选择文件 "),l[12]||(l[12]=r("p",{class:"text-xs text-gray-500 mt-1"}," 支持图片、PDF、Word文档，最大5MB ",-1))]),Z.value.length>0?(I(),s("div",R,[(I(!0),s(d,null,n(Z.value,(e,l)=>(I(),s("div",{key:l,class:"flex items-center justify-between p-2 bg-gray-50 rounded"},[r("span",S,b(e.name),1),r("button",{type:"button",onClick:e=>(e=>{Z.value.splice(e,1)})(l),class:"text-red-600 hover:text-red-800"},[j(C(U),{class:"w-4 h-4"})],8,T)]))),128))])):k("",!0)]),r("div",W,[r("button",{type:"button",onClick:le,class:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"}," 重置 "),r("button",{type:"submit",disabled:Y.value,class:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"},[Y.value?(I(),c(C(q),{key:0,class:"w-4 h-4 mr-2 animate-spin"})):k("",!0),i(" "+b(Y.value?"提交中...":"提交反馈"),1)],8,$)])],32)]),r("div",K,[r("h3",L,[j(C(F),{class:"w-5 h-5 mr-2 text-blue-600"}),l[14]||(l[14]=i(" 反馈指南 "))]),l[15]||(l[15]=t('<div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-700"><div><h4 class="font-medium mb-2">如何写好反馈？</h4><ul class="space-y-1 list-disc list-inside"><li>描述具体的问题或建议</li><li>提供详细的操作步骤</li><li>包含错误信息或截图</li><li>说明期望的结果</li></ul></div><div><h4 class="font-medium mb-2">我们的承诺</h4><ul class="space-y-1 list-disc list-inside"><li>24小时内回复您的反馈</li><li>认真对待每一条建议</li><li>及时修复报告的问题</li><li>保护您的隐私信息</li></ul></div></div>',1))])])]))}});export{N as default};
