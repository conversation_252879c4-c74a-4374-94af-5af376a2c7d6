import{d as s,m as e,q as a,z as r,_ as o,B as t}from"./vendor-CnBwbdGX.js";import{_ as n}from"./index-DfMIV_22.js";const i={class:"user-view"},d={class:"container"},c=n(s({__name:"UserView",setup:s=>(s,n)=>{const c=o("router-view");return t(),e("div",i,[a("div",d,[n[0]||(n[0]=a("h1",null,"个人中心",-1)),n[1]||(n[1]=a("p",null,"用户功能正在开发中...",-1)),r(c)])])}}),[["__scopeId","data-v-9b4e7b4c"]]);export{c as default};
